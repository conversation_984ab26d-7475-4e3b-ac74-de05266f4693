# Technology Stack

## Core Technologies
- **Python 3.9+**: Primary language
- **uv**: Package manager and environment management
- **FFmpeg/FFprobe**: Video processing engine

## Dependencies
- **loguru**: Structured logging
- **PyYAML**: Configuration file parsing
- **rich**: CLI interface and progress visualization

## Architecture
- **Concurrent processing**: ThreadPoolExecutor for parallel segment extraction
- **Dataclass patterns**: Type-safe segment representation
- **CLI-first design**: Interactive prompts with Rich-based UX
- **Configuration-driven**: YAML-based segment definitions
- **Professional encoding profiles**: Optimized FFmpeg settings for video editing workflows
- **Frame-accurate cutting**: All-Intra and ProRes support for precise temporal control

## Development Environment
- **Windows-optimized**: PowerShell batch runner
- **Virtual environment**: uv-managed isolation
- **Project structure**: Standard Python package layout
