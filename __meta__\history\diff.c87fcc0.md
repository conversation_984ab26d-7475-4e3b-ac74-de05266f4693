```diff 
diff --git a/video_splitter/README.md b/video_splitter/README.md
index ca95bd2..1a57b44 100644
--- a/video_splitter/README.md
+++ b/video_splitter/README.md
@@ -8,7 +8,8 @@ Video segmentation tool with FFmpeg integration and rich CLI interface, supporti
 - **YAML/CSV configuration**: Load segments and settings from configuration files
 - **Interactive CLI**: User-friendly prompts with Rich-based interface
 - **Concurrent processing**: Multi-threaded segment extraction for faster processing
-- **Adobe After Effects compatibility**: Re-encode segments for maximum AE compatibility
+- **Professional encoding profiles**: Optimized settings for video editing workflows
+- **Frame-accurate cutting**: All-Intra H.264 and ProRes support for precise editing
 - **Comprehensive validation**: Overlap detection and duration checking
 - **Progress tracking**: Real-time processing progress with visual indicators
 - **Flexible output naming**: Timestamp appending and source prefix options
@@ -36,9 +37,12 @@ use_subdirectory: true
 ffmpeg_path: "ffmpeg"
 ffprobe_path: "ffprobe"
 output_format: "mp4"
-codec: "copy"
+
+# Encoding profile for optimal video editing workflows
+# Options: fast_copy, h264_intra, prores_hq, legacy_ae
+codec_profile: "h264_intra"
+
 threads: 4
-after_effects_compatible: false
 append_timestamps: true
 include_source_prefix: false
 
@@ -61,6 +65,33 @@ segments:
 ## Dependencies
 Managed via `pyproject.toml` with uv package manager.
 
+## Encoding Profiles
+
+### `fast_copy` (Default)
+- **Use case**: Quick extraction when frame accuracy isn't critical
+- **Method**: Stream copy (no re-encoding)
+- **Pros**: Fastest processing, smallest files
+- **Cons**: May have seeking issues in editors, cuts only on keyframes
+
+### `h264_intra` (Recommended for Editing)
+- **Use case**: Frame-accurate editing in Premiere Pro, DaVinci Resolve, etc.
+- **Method**: All-Intra H.264 with visually lossless quality (CRF 18)
+- **Pros**: Frame-accurate cuts, smooth scrubbing, excellent quality
+- **Cons**: Larger file sizes than fast_copy
+
+### `prores_hq` (Professional)
+- **Use case**: Professional editing workflows, color grading, high-end post-production
+- **Method**: ProRes 422 HQ with 10-bit 4:2:2 color sampling and 24-bit audio
+- **Technical**: `yuv422p10le`, `pcm_s24le`, CFR sync, optimized GOP structure
+- **Pros**: Industry standard, maximum quality, best editing performance
+- **Cons**: Very large files, requires .mov container
+
+### `legacy_ae` (Compatibility)
+- **Use case**: Older After Effects versions or compatibility issues
+- **Method**: Standard H.264 with AAC audio
+- **Pros**: Wide compatibility
+- **Cons**: Not optimized for editing performance
+
 ## Command Line Options
 - `-i, --input_video`: Path to input video file
 - `-op, --output_path`: Output directory path
@@ -68,9 +99,10 @@ Managed via `pyproject.toml` with uv package manager.
 - `--ffmpeg_path`: Path to ffmpeg executable (default: "ffmpeg")
 - `--ffprobe_path`: Path to ffprobe executable (default: "ffprobe")
 - `--output_format`: Output video format (default: "mp4")
-- `--codec`: Video codec (default: "copy")
+- `--codec_profile`: Encoding profile (fast_copy, h264_intra, prores_hq, legacy_ae)
+- `--codec`: Legacy video codec option (default: "copy")
 - `--threads`: Number of concurrent threads (default: 1)
-- `--after_effects_compatible`: Re-encode for Adobe After Effects compatibility
+- `--after_effects_compatible`: DEPRECATED - Use --codec_profile legacy_ae
 - `--append_timestamps`: Append timestamps to output filenames
 - `--include_source_prefix`: Include source filename as prefix
 - `--use_subdirectory`: Create subdirectory for output files
diff --git a/video_splitter/src/main.py b/video_splitter/src/main.py
index 8e8498f..30a0090 100644
--- a/video_splitter/src/main.py
+++ b/video_splitter/src/main.py
@@ -24,16 +24,71 @@ class TimecodeParser:
 
     @staticmethod
     def normalize_timecode(time_str: str, require_hours: bool = False) -> str:
+        """Enhanced timecode parsing with explicit format detection.
+
+        Formats supported:
+        - HH:MM:SS.mmm - Hours:Minutes:Seconds.Milliseconds
+        - MM:SS.mmm - Minutes:Seconds.Milliseconds
+        - SS.mmm - Seconds.Milliseconds
+        - Smart detection for ambiguous cases like 00:17:30
+        """
         time_str = time_str.strip()
+
+        # Handle decimal seconds (e.g., "17.30", "1:30.5")
+        if '.' in time_str:
+            return TimecodeParser._parse_decimal_timecode(time_str, require_hours)
+
         if ':' in time_str:
             parts = time_str.split(':')
             if require_hours and len(parts) != 3:
                 raise ValueError("Invalid format: use HH:MM:SS.")
+
             if len(parts) == 2:
-                hours = '00'
-                minutes, seconds = parts
+                # MM:SS format - but check for seconds.milliseconds interpretation
+                minutes_part, seconds_part = parts
+                minutes_val, seconds_val = int(minutes_part), int(seconds_part)
+
+                # Smart detection: if "seconds" > 59, treat as MM:SS
+                # If both parts are <= 59 and first part has leading zeros, treat as SS.mmm
+                if seconds_val >= 60:
+                    # Definitely MM:SS format (seconds > 59)
+                    hours = '00'
+                    minutes, seconds = minutes_part, seconds_part
+                elif (minutes_part == '00' and minutes_val <= 59 and seconds_val <= 99):
+                    # For "00:XX" format, default to MM:SS (0 minutes, XX seconds)
+                    # Only treat as SS.mmm if XX has non-zero fractional part indicators
+                    # This means: XX > 59 OR XX ends in non-zero digit that suggests milliseconds
+                    if seconds_val > 59:  # Definitely milliseconds (e.g., "00:75" = 0.75 seconds)
+                        hours = '00'
+                        minutes = '00'
+                        total_seconds = minutes_val + (seconds_val / 100.0)
+                        return f"00:00:{total_seconds:06.3f}"
+                    else:
+                        # Standard MM:SS format (e.g., "00:17" = 0 minutes 17 seconds)
+                        hours = '00'
+                        minutes, seconds = minutes_part, seconds_part
+                else:
+                    # Standard MM:SS format
+                    hours = '00'
+                    minutes, seconds = minutes_part, seconds_part
+
             elif len(parts) == 3:
                 hours, minutes, seconds = parts
+                hours_val, minutes_val, seconds_val = int(hours), int(minutes), int(seconds)
+
+                # Enhanced detection for 00:SS:mmm format (e.g., "00:17:30" = 17.30 seconds)
+                # Only treat as SS:mmm if it's clearly NOT standard time format
+                # Rule: Only if seconds > 59 OR (seconds <= 99 AND minutes >= 10 AND seconds != 00)
+                if (hours_val == 0 and not require_hours and
+                    ((seconds_val > 59) or  # Definitely not standard seconds
+                     (minutes_val >= 10 and seconds_val <= 99 and seconds_val != 0))):  # Likely SS.mmm
+                    # Treat as 00:SS:mmm format (seconds.milliseconds)
+                    total_seconds = minutes_val + (seconds_val / 100.0)
+                    return f"00:00:{total_seconds:06.3f}"
+                else:
+                    # Standard HH:MM:SS format
+                    hours, minutes, seconds = hours, minutes, seconds
+
             elif len(parts) == 1:
                 hours = '00'
                 minutes = '00'
@@ -41,26 +96,10 @@ class TimecodeParser:
             else:
                 raise ValueError("Invalid format: HH:MM:SS, MM:SS, or SS expected.")
         else:
-            # Possibly HHMMSS or MMSS or SS
-            import re
-            digits = ''.join(re.findall(r'\d+', time_str))
-            length = len(digits)
-            if require_hours:
-                if length != 6:
-                    raise ValueError("Invalid length: use HHMMSS.")
-                hours, minutes, seconds = digits[:2], digits[2:4], digits[4:6]
-            else:
-                if length == 4:  # e.g. "0130" => "00:01:30"
-                    hours = '00'
-                    minutes = digits[:2]
-                    seconds = digits[2:4]
-                elif length == 2:  # "45" => "00:00:45"
-                    hours = '00'
-                    minutes = '00'
-                    seconds = digits
-                else:
-                    raise ValueError("Invalid length: use MMSS or SS.")
+            # Handle numeric-only formats (HHMMSS, MMSS, SS)
+            return TimecodeParser._parse_numeric_timecode(time_str, require_hours)
 
+        # Validate components
         hours, minutes, seconds = int(hours), int(minutes), int(seconds)
         if not (0 <= minutes < 60) or not (0 <= seconds < 60) or hours < 0:
             raise ValueError("Invalid time components.")
@@ -73,18 +112,69 @@ class TimecodeParser:
             return f"{seconds:02}"
 
     @staticmethod
-    def parse_timecode(time_str: str, require_hours: bool = False) -> int:
+    def _parse_decimal_timecode(time_str: str, require_hours: bool = False) -> str:
+        """Parse timecode with decimal seconds (e.g., '17.30', '1:30.5')."""
+        if ':' in time_str:
+            # Format like "1:30.5" or "0:17.30"
+            parts = time_str.split(':')
+            if len(parts) == 2:
+                minutes_part, seconds_part = parts
+                minutes = int(minutes_part)
+                seconds = float(seconds_part)
+                total_seconds = minutes * 60 + seconds
+                return f"00:00:{total_seconds:06.3f}"
+            else:
+                raise ValueError("Decimal format supports MM:SS.mmm only.")
+        else:
+            # Format like "17.30" (seconds.milliseconds)
+            seconds = float(time_str)
+            return f"00:00:{seconds:06.3f}"
+
+    @staticmethod
+    def _parse_numeric_timecode(time_str: str, require_hours: bool = False) -> str:
+        """Parse numeric-only timecode (HHMMSS, MMSS, SS)."""
+        import re
+        digits = ''.join(re.findall(r'\d+', time_str))
+        length = len(digits)
+
+        if require_hours:
+            if length != 6:
+                raise ValueError("Invalid length: use HHMMSS.")
+            hours, minutes, seconds = digits[:2], digits[2:4], digits[4:6]
+        else:
+            if length == 6:  # HHMMSS
+                hours, minutes, seconds = digits[:2], digits[2:4], digits[4:6]
+            elif length == 4:  # MMSS
+                hours = '00'
+                minutes, seconds = digits[:2], digits[2:4]
+            elif length == 2:  # SS
+                hours = '00'
+                minutes = '00'
+                seconds = digits
+            else:
+                raise ValueError("Invalid length: use HHMMSS, MMSS, or SS.")
+
+        return f"{hours}:{minutes}:{seconds}"
+
+    @staticmethod
+    def parse_timecode(time_str: str, require_hours: bool = False) -> float:
+        """Parse timecode and return total seconds as float for precision."""
         normalized = TimecodeParser.normalize_timecode(time_str, require_hours)
         parts = normalized.split(':')
+
         if len(parts) == 3:
-            hours, minutes, seconds = map(int, parts)
+            hours, minutes = int(parts[0]), int(parts[1])
+            # Handle decimal seconds
+            seconds = float(parts[2]) if '.' in parts[2] else int(parts[2])
         elif len(parts) == 2:
             hours = 0
-            minutes, seconds = map(int, parts)
+            minutes = int(parts[0])
+            seconds = float(parts[1]) if '.' in parts[1] else int(parts[1])
         else:
             hours = 0
             minutes = 0
-            seconds = int(parts[0])
+            seconds = float(parts[0]) if '.' in parts[0] else int(parts[0])
+
         return hours * 3600 + minutes * 60 + seconds
 
     @staticmethod
@@ -122,9 +212,12 @@ def parse_arguments():
     parser.add_argument("--ffprobe_path", default="ffprobe", help="Path to ffprobe executable")
     parser.add_argument("--output_format", default="mp4", help="Output video format")
     parser.add_argument("--codec", default="copy", help="Video codec (copy, libx264, etc.)")
+    parser.add_argument("--codec_profile", default="fast_copy",
+                        choices=["fast_copy", "h264_intra", "prores_hq", "legacy_ae"],
+                        help="Encoding profile: fast_copy (stream copy), h264_intra (All-Intra H.264), prores_hq (ProRes 422 HQ), legacy_ae (legacy AE compatibility)")
     parser.add_argument("--threads", type=int, default=1, help="Number of concurrent threads")
-    parser.add_argument("--after_effects_compatible", action="store_true", 
-                        help="Re-encode for Adobe After Effects compatibility")
+    parser.add_argument("--after_effects_compatible", action="store_true",
+                        help="DEPRECATED: Use --codec_profile legacy_ae instead")
     parser.add_argument("--append_timestamps", action="store_true", default=True,
                         help="Append timestamps to output filenames")
     parser.add_argument("--include_source_prefix", action="store_true", default=False,
@@ -209,6 +302,7 @@ def load_timecodes_from_yaml(file_path: str) -> Dict[str, Any]:
         'ffprobe_path': data.get('ffprobe_path', 'ffprobe'),
         'output_format': data.get('output_format', 'mp4'),
         'codec': data.get('codec', 'copy'),
+        'codec_profile': data.get('codec_profile', 'fast_copy'),
         'threads': data.get('threads', 1),
         'after_effects_compatible': data.get('after_effects_compatible', False),
         'append_timestamps': data.get('append_timestamps', True),
@@ -405,10 +499,24 @@ def get_user_inputs(args):
             "[bold cyan]Include source file's name as prefix?[/bold cyan]",
             default=yaml_config.get("include_source_prefix", False)
         )
-        args.after_effects_compatible = Confirm.ask(
-            "[bold cyan]Re-encode segments for Adobe After Effects compatibility?[/bold cyan]",
-            default=yaml_config.get("after_effects_compatible", False)
+        # Codec profile selection
+        console.print("\n[bold cyan]Encoding Profile Options:[/bold cyan]")
+        console.print("  [dim]fast_copy[/dim]    - Stream copy (fastest, may have seeking issues)")
+        console.print("  [dim]h264_intra[/dim]   - All-Intra H.264 (frame-accurate, editing-optimized)")
+        console.print("  [dim]prores_hq[/dim]    - ProRes 422 HQ (professional mezzanine)")
+        console.print("  [dim]legacy_ae[/dim]    - Legacy After Effects compatibility")
+
+        default_profile = yaml_config.get("codec_profile", "fast_copy")
+        args.codec_profile = Prompt.ask(
+            "[bold cyan]Select encoding profile[/bold cyan]",
+            choices=["fast_copy", "h264_intra", "prores_hq", "legacy_ae"],
+            default=default_profile
         )
+
+        # Handle legacy after_effects_compatible flag
+        if yaml_config.get("after_effects_compatible", False):
+            args.codec_profile = "legacy_ae"
+            console.print("[yellow]Note: Using legacy_ae profile based on after_effects_compatible setting[/yellow]")
     elif not args.output_path:
         default_output = yaml_config.get('output_dir') or args.output_path
         if not default_output:
@@ -450,11 +558,12 @@ def get_user_inputs(args):
 
     return args.input_video, args.output_path, segments, args
 
-def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str, 
+def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str,
                        ffmpeg_path: str, output_format: str, codec: str,
                        append_timestamps: bool, include_source_prefix: bool,
-                       after_effects_compatible: bool) -> Tuple[str, Optional[str], Optional[str]]:
+                       after_effects_compatible: bool, codec_profile: str = "fast_copy") -> Tuple[str, Optional[str], Optional[str]]:
     """Split a single video segment using FFmpeg."""
+
     source_name = Path(input_video).stem if include_source_prefix else ""
     prefix = f"{source_name}_" if include_source_prefix else ""
     
@@ -465,22 +574,79 @@ def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str
     else:
         filename = f"{prefix}{segment.name}.{output_format}"
     
+    # Determine output format and container based on codec profile
+    if codec_profile == "prores_hq":
+        output_format = "mov"  # ProRes requires MOV container
+        filename = filename.replace(".mp4", ".mov")
+
     output_path = Path(output_dir) / filename
-    
-    if after_effects_compatible:
-        cmd = [
-            ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
+
+    # Build FFmpeg command based on codec profile
+    cmd = [ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end]
+
+    if codec_profile == "fast_copy":
+        # Stream copy - fastest but may have seeking issues
+        cmd.extend([
+            "-c", "copy",
+            "-avoid_negative_ts", "make_zero",
+            "-y", str(output_path)
+        ])
+
+    elif codec_profile == "h264_intra":
+        # All-Intra H.264 - optimal for editing workflows (enhanced settings)
+        cmd.extend([
+            "-c:v", "libx264", "-preset", "medium", "-crf", "18", "-g", "1",  # All-Intra
+            "-bf", "0",  # No B-frames for simpler structure
+            "-pix_fmt", "yuv420p", "-color_primaries", "bt709", "-color_trc", "bt709",
+            "-colorspace", "bt709", "-color_range", "tv",
+            "-c:a", "pcm_s24le", "-ar", "48000",  # 24-bit audio at 48kHz
+            "-vsync", "cfr",  # Constant frame rate
+            "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
+            "-y", str(output_path)
+        ])
+
+    elif codec_profile == "prores_hq":
+        # ProRes 422 HQ - professional mezzanine codec (enhanced settings)
+        cmd.extend([
+            "-c:v", "prores_ks", "-profile:v", "3",  # ProRes 422 HQ
+            "-pix_fmt", "yuv422p10le",  # 10-bit 4:2:2 for maximum quality
+            "-vendor", "apl0",  # Apple vendor tag (preferred)
+            "-bits_per_mb", "8000",  # Quality control for consistent bitrate
+            "-g", "30", "-bf", "0",  # GOP structure optimized for editing
+            "-colorspace", "bt709", "-color_primaries", "bt709", "-color_trc", "bt709",
+            "-c:a", "pcm_s24le", "-ar", "48000",  # 24-bit audio at 48kHz
+            "-vsync", "cfr",  # Constant frame rate for editing compatibility
+            "-avoid_negative_ts", "make_zero",
+            "-y", str(output_path)
+        ])
+
+    elif codec_profile == "legacy_ae" or after_effects_compatible:
+        # Legacy After Effects compatibility mode
+        cmd.extend([
             "-c:v", "libx264", "-preset", "faster", "-pix_fmt", "yuv420p",
-            "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart", "-y", str(output_path)
-        ]
+            "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart",
+            "-y", str(output_path)
+        ])
+
     else:
-        # Use output seeking (-ss after -i) for frame-accurate cuts that match video editors
-        # This ensures precise timing alignment with editing software
-        cmd = [
-            ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
-            "-c:v", "libx264", "-c:a", "aac", "-y", str(output_path)
-        ]
+        # Fallback to legacy codec-based logic for backward compatibility
+        if codec == "copy":
+            cmd.extend([
+                "-c", "copy", "-avoid_negative_ts", "make_zero", "-y", str(output_path)
+            ])
+        else:
+            # Enhanced settings for video editing workflows
+            cmd.extend([
+                "-c:v", codec, "-preset", "medium", "-crf", "18", "-g", "6", "-bf", "0",
+                "-pix_fmt", "yuv420p", "-colorspace", "bt709", "-color_primaries", "bt709",
+                "-color_trc", "bt709", "-color_range", "mpeg",
+                "-c:a", "aac", "-b:a", "192k", "-ar", "48000",
+                "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
+                "-y", str(output_path)
+            ])
     
+
+
     try:
         subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
         return segment.name, str(output_path), None
@@ -523,7 +689,8 @@ def process_video_segments(input_video: str, output_dir: str,
                     config.codec,
                     config.append_timestamps,
                     config.include_source_prefix,
-                    config.after_effects_compatible
+                    config.after_effects_compatible,
+                    getattr(config, 'codec_profile', 'fast_copy')
                 ): segment.name for segment in segment_objects
             }
 
@@ -558,10 +725,11 @@ def display_summary(input_video: str, output_dir: str,
     table.add_row("Input Video", input_video)
     table.add_row("Output Directory", output_dir)
     table.add_row("Output Format", config.output_format)
-    table.add_row("Codec", config.codec)
+    table.add_row("Codec Profile", getattr(config, 'codec_profile', 'fast_copy'))
+    table.add_row("Legacy Codec", config.codec)
     table.add_row("Threads", str(config.threads))
     ae_compat = "Yes" if config.after_effects_compatible else "No"
-    table.add_row("AE Compatible", ae_compat)
+    table.add_row("AE Compatible (Legacy)", ae_compat)
     append_ts = "Yes" if config.append_timestamps else "No"
     table.add_row("Append Timestamps", append_ts)
     include_prefix = "Yes" if config.include_source_prefix else "No"
@@ -653,5 +821,7 @@ def main():
         wait_for_user_exit()
 
 
+
+
 if __name__ == "__main__":
     main()
diff --git a/video_splitter/techstack.md b/video_splitter/techstack.md
index 792c368..0a43719 100644
--- a/video_splitter/techstack.md
+++ b/video_splitter/techstack.md
@@ -15,6 +15,8 @@
 - **Dataclass patterns**: Type-safe segment representation
 - **CLI-first design**: Interactive prompts with Rich-based UX
 - **Configuration-driven**: YAML-based segment definitions
+- **Professional encoding profiles**: Optimized FFmpeg settings for video editing workflows
+- **Frame-accurate cutting**: All-Intra and ProRes support for precise temporal control
 
 ## Development Environment
 - **Windows-optimized**: PowerShell batch runner
diff --git a/video_splitter/timecodes/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed-timecodes.yaml b/video_splitter/timecodes/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed-timecodes.yaml
index 968569d..434c534 100644
--- a/video_splitter/timecodes/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed-timecodes.yaml
+++ b/video_splitter/timecodes/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed-timecodes.yaml
@@ -2,9 +2,13 @@
 
 # 2025.01.22-kl.10.49.34--Perspektiv_merged_compressed
 
-input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
+# input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
+# use_subdirectory: true
+# output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv"
+
+input_video: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
 use_subdirectory: true
-output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv"
+output_dir: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter"
 
 ffmpeg_path: "ffmpeg"
 ffprobe_path: "ffprobe"
@@ -16,7 +20,7 @@ append_timestamps: true
 include_source_prefix: false
 
 segments:
-  - start: "00:00"
+  - start: "00:06"
     end: "00:17"
     title: "a-starte videoopptak"
 
@@ -24,10 +28,10 @@ segments:
   #   end: "04:44"
   #   title: "b-dele perspektiv"
 
-  - start: "04:54:00"
-    end: "06:43:20"
-    title: "c-det uperfekte, hånd i hånd"
-    # title: "c-vi står hånd i hånd"
+  # - start: "04:54:00"
+  #   end: "06:43:20"
+  #   title: "c-det uperfekte, hånd i hånd"
+  #   # title: "c-vi står hånd i hånd"
 
   - start: "07:01:16"
     end: "09:39:12"
diff --git a/video_splitter/timecodes/editing-optimized-example.yaml b/video_splitter/timecodes/editing-optimized-example.yaml
new file mode 100644
index 0000000..b2cb715
--- /dev/null
+++ b/video_splitter/timecodes/editing-optimized-example.yaml
@@ -0,0 +1,71 @@
+# editing-optimized-example.yaml
+# Professional video editing workflow configuration
+
+input_video: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
+use_subdirectory: true
+output_dir: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter"
+
+ffmpeg_path: "ffmpeg"
+ffprobe_path: "ffprobe"
+
+# ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
+# ==============================================
+
+# RECOMMENDED: All-Intra H.264 for frame-accurate editing
+# - Every frame is a keyframe (I-frame) for precise cuts
+# - CRF 18 provides visually lossless quality
+# - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
+codec_profile: "h264_intra"
+output_format: "mp4"
+
+# ALTERNATIVE: ProRes 422 HQ for professional workflows
+# Uncomment the lines below for ProRes output:
+# codec_profile: "prores_hq"
+# output_format: "mov"  # ProRes requires MOV container
+
+# ALTERNATIVE: Fast stream copy for quick previews
+# codec_profile: "fast_copy"
+
+# ALTERNATIVE: Legacy After Effects compatibility
+# codec_profile: "legacy_ae"
+
+threads: 4
+append_timestamps: true
+include_source_prefix: false
+
+# TECHNICAL NOTES:
+# ================
+# h264_intra settings:
+#   - All-Intra encoding (-g 1) ensures every frame is seekable
+#   - CRF 18 provides visually lossless quality
+#   - PCM audio avoids compression artifacts
+#   - BT.709 color space prevents color shifts
+#
+# prores_hq settings:
+#   - ProRes 422 HQ profile for high-quality intermediate files
+#   - Uncompressed PCM audio
+#   - Industry standard for professional editing
+#
+# fast_copy settings:
+#   - Stream copy for maximum speed
+#   - May have seeking issues on P-frames and B-frames
+#   - Best for quick previews or when frame accuracy isn't critical
+
+
+segments:
+  - start: "00:00:00"
+    end: "00:17:00"
+    title: "a-starte videoopptak"
+
+  - start: "00:34:00"
+    end: "04:44:00"
+    title: "b-dele perspektiv"
+
+  - start: "04:54:00"
+    end: "06:43:20"
+    title: "c-det uperfekte, hånd i hånd"
+    # title: "c-vi står hånd i hånd"
+
+  - start: "07:01:16"
+    end: "09:39:12"
+    title: "d-oppmerksomheten vår"
diff --git a/video_splitter/timecodes/input-timecodes.yaml b/video_splitter/timecodes/input-timecodes.yaml
index 9347db2..c6703f5 100644
--- a/video_splitter/timecodes/input-timecodes.yaml
+++ b/video_splitter/timecodes/input-timecodes.yaml
@@ -8,21 +8,42 @@ output_dir: "output_segments"
 ffmpeg_path: "ffmpeg"
 ffprobe_path: "ffprobe"
 output_format: "mp4"
-codec: "copy"
-threads: 4
+
+# ENCODING PROFILE OPTIONS:
+# fast_copy    - Stream copy (fastest, may have seeking issues)
+# h264_intra   - All-Intra H.264 (frame-accurate, editing-optimized)
+# prores_hq    - ProRes 422 HQ (professional mezzanine, requires .mov)
+# legacy_ae    - Legacy After Effects compatibility
+codec_profile: "h264_intra"
+
+# Legacy settings (deprecated - use codec_profile instead)
+codec: "libx264"  # Use "libx264" for frame-accurate seeking, "copy" for speed
 after_effects_compatible: false
+
+threads: 4
 append_timestamps: true
 include_source_prefix: true
 
+# TIMECODE FORMAT GUIDE:
+# For seconds with milliseconds, use explicit decimal format to avoid ambiguity:
+#   "17.30"       = 17.30 seconds (RECOMMENDED for sub-minute timing)
+#   "0:00:17.30"  = 17.30 seconds (explicit format)
+#   "00:17:30"    = 17.30 seconds (auto-detected when >= 10 seconds)
+#   "00:01:30"    = 1 minute 30 seconds (auto-detected as MM:SS)
+#
+# For longer durations:
+#   "0:04:41.14"  = 4 minutes 41.14 seconds
+#   "1:30:45.00"  = 1 hour 30 minutes 45 seconds
+
 segments:
-  - start: "00:46"
-    end: "01:16"
+  - start: "0:00:46.00"
+    end: "0:01:16.00"
     title: "Introduction"
 
-  - start: "01:17"
-    end: "05:17"
+  - start: "0:01:17.00"
+    end: "0:05:17.00"
     title: "Main Content"
 
-  - start: "08:21"
-    end: "11:12"
+  - start: "0:08:21.00"
+    end: "0:11:12.00"
     title: "Conclusion"

```
