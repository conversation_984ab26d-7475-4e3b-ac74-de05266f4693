# editing-optimized-example.yaml
# Professional video editing workflow configuration

input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.02-kl.12.52.18--Perspektiv_a_compressed.mp4"
use_subdirectory: true
output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.02"

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"

# ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
# ==============================================

# RECOMMENDED: All-Intra H.264 for frame-accurate editing
# - Every frame is a keyframe (I-frame) for precise cuts
# - CRF 18 provides visually lossless quality
# - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
codec_profile: "h264_intra"
output_format: "mp4"

# QUALITY CONTROL FOR FILE SIZE
# =============================
# Quality setting (0-51): Controls file size vs quality trade-off
# - 0: Lossless (largest files, perfect quality)
# - 18: Visually lossless (default, excellent quality)
# - 23: Standard quality (good balance)
# - 28: Smaller files (good quality, noticeable compression)
# - 35: Much smaller files (acceptable quality, visible artifacts)
quality: 18

# ALTERNATIVE: ProRes 422 HQ for professional workflows
# Uncomment the lines below for ProRes output:
# codec_profile: "prores_hq"
# output_format: "mov"  # ProRes requires MOV container

# ALTERNATIVE: Fast stream copy for quick previews (smallest files)
# codec_profile: "fast_copy"
# Note: fast_copy ignores quality setting

# ALTERNATIVE: Legacy After Effects compatibility
# codec_profile: "legacy_ae"

threads: 4
append_timestamps: true
include_source_prefix: false

# TECHNICAL NOTES:
# ================
# h264_intra settings:
#   - All-Intra encoding (-g 1) ensures every frame is seekable
#   - CRF 18 provides visually lossless quality
#   - PCM audio avoids compression artifacts
#   - BT.709 color space prevents color shifts
#
# prores_hq settings:
#   - ProRes 422 HQ profile for high-quality intermediate files
#   - Uncompressed PCM audio
#   - Industry standard for professional editing
#
# fast_copy settings:
#   - Stream copy for maximum speed
#   - May have seeking issues on P-frames and B-frames
#   - Best for quick previews or when frame accuracy isn't critical

# TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
# Examples:
#   "00:00:17" = 17 seconds
#   "00:04:44" = 4 minutes 44 seconds
#   "00:00:17.500" = 17.5 seconds (500ms)
#   "00:01:30.250" = 1 minute 30.25 seconds (250ms)

segments:
  - start: "00:14:37.24"
    end: "00:17:57.25"
    title: "02-a-de som beveger seg rundt med lukkede øyne"

  - start: "00:18:15.24"
    end: "00:19:31.28"
    title: "02-b-ikke alt i livet man skal unngå"

  - start: "00:19:46.15"
    end: "00:19:52.24"
    title: "02-c-bare går sporene jeg har gått"

  - start: "00:20:00.26"
    end: "00:20:05.03"
    title: "02-d-suger til meg alt jeg kan"

  - start: "00:20:11.16"
    end: "00:22:08.06"
    title: "02-e-hva som er mistet"
