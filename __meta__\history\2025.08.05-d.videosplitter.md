<!-- ======================================================= -->
<!-- [2025.08.05 23:42] -->

This utility is a Python CLI tool that splits video files into multiple segments based on timecode ranges defined in YAML configuration files, using FFmpeg for frame-accurate cutting with optimized settings for video editing workflows. Please conduct a comprehensive familiarization of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Provide a short summary of the project; Your goal is not to **explain** the principles, but to **distill** them into concentrated insights with maximum presicion.

<!-- ======================================================= -->
<!-- [2025.08.05 23:44] -->

<!-- ======================================================= -->
<!-- [2025.08.05 23:47] -->
<!-- ======================================================= -->
<!-- [2025.08.05 23:47] -->

Research topics related to the provided code with the goal of finding the optimal ffmpeg video export settings for video editorss

    # Dir `video_splitter`

    ### File Structure

    ```
    ├── README.md
    ├── pyproject.toml
    ├── run.bat
    ├── techstack.md
    ├── uv.lock
    ├── src
    │   └── main.py
    └── timecodes
        ├── 2025.01.22-kl.10.49.34--Perspektiv_merged_compressed-timecodes.yaml
        └── input-timecodes.yaml
    ```

    ---

    #### `README.md`

    ```markdown
        # Video Splitter

        ## Overview
        Video segmentation tool with FFmpeg integration and rich CLI interface, supporting precise time-based video splitting with YAML/CSV configuration.

        ## Features
        - **Flexible time formats**: Support for HH:MM:SS, MM:SS, and numeric formats
        - **YAML/CSV configuration**: Load segments and settings from configuration files
        - **Interactive CLI**: User-friendly prompts with Rich-based interface
        - **Concurrent processing**: Multi-threaded segment extraction for faster processing
        - **Adobe After Effects compatibility**: Re-encode segments for maximum AE compatibility
        - **Comprehensive validation**: Overlap detection and duration checking
        - **Progress tracking**: Real-time processing progress with visual indicators
        - **Flexible output naming**: Timestamp appending and source prefix options

        ## Quick Start
        Run `run.bat` to start the interactive video splitter (handles environment setup automatically)

        ## Usage
        ```bash
        # Interactive mode (recommended)
        run.bat

        # Direct command line usage
        uv run python src/main.py --prompt
        uv run python src/main.py -i "video.mp4" -op "output/" -tc "timecodes.yaml"
        ```

        ## Configuration File Example
        ```yaml
        # timecodes.yaml
        input_video: "path/to/video.mp4"
        output_dir: "output/segments"
        use_subdirectory: true

        ffmpeg_path: "ffmpeg"
        ffprobe_path: "ffprobe"
        output_format: "mp4"
        codec: "copy"
        threads: 4
        after_effects_compatible: false
        append_timestamps: true
        include_source_prefix: false

        segments:
          - start: "00:46"
            end: "01:16"
            title: "Introduction"
          - start: "01:17"
            end: "05:17"
            title: "Main Content"
          - start: "08:21"
            end: "11:12"
            title: "Conclusion"
        ```

        ## Requirements
        - **FFmpeg**: Required for video processing (must be in PATH or specify path)
        - **FFprobe**: Required for video metadata extraction (usually comes with FFmpeg)

        ## Dependencies
        Managed via `pyproject.toml` with uv package manager.

        ## Command Line Options
        - `-i, --input_video`: Path to input video file
        - `-op, --output_path`: Output directory path
        - `-tc, --timecodes_file`: Path to YAML/CSV timecodes file
        - `--ffmpeg_path`: Path to ffmpeg executable (default: "ffmpeg")
        - `--ffprobe_path`: Path to ffprobe executable (default: "ffprobe")
        - `--output_format`: Output video format (default: "mp4")
        - `--codec`: Video codec (default: "copy")
        - `--threads`: Number of concurrent threads (default: 1)
        - `--after_effects_compatible`: Re-encode for Adobe After Effects compatibility
        - `--append_timestamps`: Append timestamps to output filenames
        - `--include_source_prefix`: Include source filename as prefix
        - `--use_subdirectory`: Create subdirectory for output files
        - `--prompt`: Interactive mode
    ```

    ---

    #### `pyproject.toml`

    ```toml
        [project]
        name = "video-splitter"
        version = "1.0.0"
        description = "Video segmentation tool with FFmpeg integration and rich CLI interface"
        requires-python = ">=3.9"

        dependencies = [
            "loguru",
            "PyYAML",
            "rich",
        ]
    ```

    ---

    #### `run.bat`

    ```batch
        @ECHO OFF
        SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
        :: =============================================================================
        :: Video Splitter - Universal Runner
        :: =============================================================================
        IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

        :: Check for uv installation
        WHERE uv >nul 2>&1
        IF ERRORLEVEL 1 (
            ECHO [ERROR] uv is not installed or not in PATH
            ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
            ECHO.
            ECHO Falling back to traditional Python execution...
            python src\main.py --prompt %*
            GOTO :End
        )

        :: Check for pyproject.toml
        IF NOT EXIST "pyproject.toml" (
            ECHO [ERROR] pyproject.toml not found
            ECHO Please ensure the project is properly configured
            PAUSE>NUL & EXIT /B
        )

        :: Initialize environment if needed
        IF NOT EXIST ".venv" (
            ECHO [INFO] Initializing uv environment...
            uv sync
            IF ERRORLEVEL 1 (
                ECHO [ERROR] Failed to initialize environment
                PAUSE>NUL & EXIT /B
            )
            ECHO [SUCCESS] Environment initialized
            ECHO.
        )

        :: Run the application
        ECHO [INFO] Starting Video Splitter...
        uv run python src\main.py --prompt %*

        :End
        ECHO.
        ECHO Press any key to exit...
        PAUSE >NUL
        EXIT /B
    ```

    ---

    #### `techstack.md`

    ```markdown
        # Technology Stack

        ## Core Technologies
        - **Python 3.9+**: Primary language
        - **uv**: Package manager and environment management
        - **FFmpeg/FFprobe**: Video processing engine

        ## Dependencies
        - **loguru**: Structured logging
        - **PyYAML**: Configuration file parsing
        - **rich**: CLI interface and progress visualization

        ## Architecture
        - **Concurrent processing**: ThreadPoolExecutor for parallel segment extraction
        - **Dataclass patterns**: Type-safe segment representation
        - **CLI-first design**: Interactive prompts with Rich-based UX
        - **Configuration-driven**: YAML-based segment definitions

        ## Development Environment
        - **Windows-optimized**: PowerShell batch runner
        - **Virtual environment**: uv-managed isolation
        - **Project structure**: Standard Python package layout
    ```

    ---

    #### `uv.lock`

    ```lock
        version = 1
        revision = 2
        requires-python = ">=3.9"

        [[package]]
        name = "colorama"
        version = "0.4.6"
        source = { registry = "https://pypi.org/simple" }
        sdist = { url = "https://files.pythonhosted.org/packages/d8/53/6f443c9a4a8358a93a6792e2acffb9d9d5cb0a5cfd8802644b7b1c9a02e4/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44", size = 27697, upload-time = "2022-10-25T02:36:22.414Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", size = 25335, upload-time = "2022-10-25T02:36:20.889Z" },
        ]

        [[package]]
        name = "loguru"
        version = "0.7.3"
        source = { registry = "https://pypi.org/simple" }
        dependencies = [
            { name = "colorama", marker = "sys_platform == 'win32'" },
            { name = "win32-setctime", marker = "sys_platform == 'win32'" },
        ]
        sdist = { url = "https://files.pythonhosted.org/packages/3a/05/a1dae3dffd1116099471c643b8924f5aa6524411dc6c63fdae648c4f1aca/loguru-0.7.3.tar.gz", hash = "sha256:19480589e77d47b8d85b2c827ad95d49bf31b0dcde16593892eb51dd18706eb6", size = 63559, upload-time = "2024-12-06T11:20:56.608Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/0c/29/0348de65b8cc732daa3e33e67806420b2ae89bdce2b04af740289c5c6c8c/loguru-0.7.3-py3-none-any.whl", hash = "sha256:31a33c10c8e1e10422bfd431aeb5d351c7cf7fa671e3c4df004162264b28220c", size = 61595, upload-time = "2024-12-06T11:20:54.538Z" },
        ]

        [[package]]
        name = "markdown-it-py"
        version = "3.0.0"
        source = { registry = "https://pypi.org/simple" }
        dependencies = [
            { name = "mdurl" },
        ]
        sdist = { url = "https://files.pythonhosted.org/packages/38/71/3b932df36c1a044d397a1f92d1cf91ee0a503d91e470cbd670aa66b07ed0/markdown-it-py-3.0.0.tar.gz", hash = "sha256:e3f60a94fa066dc52ec76661e37c851cb232d92f9886b15cb560aaada2df8feb", size = 74596, upload-time = "2023-06-03T06:41:14.443Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl", hash = "sha256:355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1", size = 87528, upload-time = "2023-06-03T06:41:11.019Z" },
        ]

        [[package]]
        name = "mdurl"
        version = "0.1.2"
        source = { registry = "https://pypi.org/simple" }
        sdist = { url = "https://files.pythonhosted.org/packages/d6/54/cfe61301667036ec958cb99bd3efefba235e65cdeb9c84d24a8293ba1d90/mdurl-0.1.2.tar.gz", hash = "sha256:bb413d29f5eea38f31dd4754dd7377d4465116fb207585f97bf925588687c1ba", size = 8729, upload-time = "2022-08-14T12:40:10.846Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl", hash = "sha256:84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8", size = 9979, upload-time = "2022-08-14T12:40:09.779Z" },
        ]

        [[package]]
        name = "pygments"
        version = "2.19.2"
        source = { registry = "https://pypi.org/simple" }
        sdist = { url = "https://files.pythonhosted.org/packages/b0/77/a5b8c569bf593b0140bde72ea885a803b82086995367bf2037de0159d924/pygments-2.19.2.tar.gz", hash = "sha256:636cb2477cec7f8952536970bc533bc43743542f70392ae026374600add5b887", size = 4968631, upload-time = "2025-06-21T13:39:12.283Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl", hash = "sha256:86540386c03d588bb81d44bc3928634ff26449851e99741617ecb9037ee5ec0b", size = 1225217, upload-time = "2025-06-21T13:39:07.939Z" },
        ]

        [[package]]
        name = "pyyaml"
        version = "6.0.2"
        source = { registry = "https://pypi.org/simple" }
        sdist = { url = "https://files.pythonhosted.org/packages/54/ed/79a089b6be93607fa5cdaedf301d7dfb23af5f25c398d5ead2525b063e17/pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e", size = 130631, upload-time = "2024-08-06T20:33:50.674Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/9b/95/a3fac87cb7158e231b5a6012e438c647e1a87f09f8e0d123acec8ab8bf71/PyYAML-6.0.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:0a9a2848a5b7feac301353437eb7d5957887edbf81d56e903999a75a3d743086", size = 184199, upload-time = "2024-08-06T20:31:40.178Z" },
            { url = "https://files.pythonhosted.org/packages/c7/7a/68bd47624dab8fd4afbfd3c48e3b79efe09098ae941de5b58abcbadff5cb/PyYAML-6.0.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:29717114e51c84ddfba879543fb232a6ed60086602313ca38cce623c1d62cfbf", size = 171758, upload-time = "2024-08-06T20:31:42.173Z" },
            { url = "https://files.pythonhosted.org/packages/49/ee/14c54df452143b9ee9f0f29074d7ca5516a36edb0b4cc40c3f280131656f/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:8824b5a04a04a047e72eea5cec3bc266db09e35de6bdfe34c9436ac5ee27d237", size = 718463, upload-time = "2024-08-06T20:31:44.263Z" },
            { url = "https://files.pythonhosted.org/packages/4d/61/de363a97476e766574650d742205be468921a7b532aa2499fcd886b62530/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:7c36280e6fb8385e520936c3cb3b8042851904eba0e58d277dca80a5cfed590b", size = 719280, upload-time = "2024-08-06T20:31:50.199Z" },
            { url = "https://files.pythonhosted.org/packages/6b/4e/1523cb902fd98355e2e9ea5e5eb237cbc5f3ad5f3075fa65087aa0ecb669/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ec031d5d2feb36d1d1a24380e4db6d43695f3748343d99434e6f5f9156aaa2ed", size = 751239, upload-time = "2024-08-06T20:31:52.292Z" },
            { url = "https://files.pythonhosted.org/packages/b7/33/5504b3a9a4464893c32f118a9cc045190a91637b119a9c881da1cf6b7a72/PyYAML-6.0.2-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:936d68689298c36b53b29f23c6dbb74de12b4ac12ca6cfe0e047bedceea56180", size = 695802, upload-time = "2024-08-06T20:31:53.836Z" },
            { url = "https://files.pythonhosted.org/packages/5c/20/8347dcabd41ef3a3cdc4f7b7a2aff3d06598c8779faa189cdbf878b626a4/PyYAML-6.0.2-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:23502f431948090f597378482b4812b0caae32c22213aecf3b55325e049a6c68", size = 720527, upload-time = "2024-08-06T20:31:55.565Z" },
            { url = "https://files.pythonhosted.org/packages/be/aa/5afe99233fb360d0ff37377145a949ae258aaab831bde4792b32650a4378/PyYAML-6.0.2-cp310-cp310-win32.whl", hash = "sha256:2e99c6826ffa974fe6e27cdb5ed0021786b03fc98e5ee3c5bfe1fd5015f42b99", size = 144052, upload-time = "2024-08-06T20:31:56.914Z" },
            { url = "https://files.pythonhosted.org/packages/b5/84/0fa4b06f6d6c958d207620fc60005e241ecedceee58931bb20138e1e5776/PyYAML-6.0.2-cp310-cp310-win_amd64.whl", hash = "sha256:a4d3091415f010369ae4ed1fc6b79def9416358877534caf6a0fdd2146c87a3e", size = 161774, upload-time = "2024-08-06T20:31:58.304Z" },
            { url = "https://files.pythonhosted.org/packages/f8/aa/7af4e81f7acba21a4c6be026da38fd2b872ca46226673c89a758ebdc4fd2/PyYAML-6.0.2-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:cc1c1159b3d456576af7a3e4d1ba7e6924cb39de8f67111c735f6fc832082774", size = 184612, upload-time = "2024-08-06T20:32:03.408Z" },
            { url = "https://files.pythonhosted.org/packages/8b/62/b9faa998fd185f65c1371643678e4d58254add437edb764a08c5a98fb986/PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:1e2120ef853f59c7419231f3bf4e7021f1b936f6ebd222406c3b60212205d2ee", size = 172040, upload-time = "2024-08-06T20:32:04.926Z" },
            { url = "https://files.pythonhosted.org/packages/ad/0c/c804f5f922a9a6563bab712d8dcc70251e8af811fce4524d57c2c0fd49a4/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5d225db5a45f21e78dd9358e58a98702a0302f2659a3c6cd320564b75b86f47c", size = 736829, upload-time = "2024-08-06T20:32:06.459Z" },
            { url = "https://files.pythonhosted.org/packages/51/16/6af8d6a6b210c8e54f1406a6b9481febf9c64a3109c541567e35a49aa2e7/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5ac9328ec4831237bec75defaf839f7d4564be1e6b25ac710bd1a96321cc8317", size = 764167, upload-time = "2024-08-06T20:32:08.338Z" },
            { url = "https://files.pythonhosted.org/packages/75/e4/2c27590dfc9992f73aabbeb9241ae20220bd9452df27483b6e56d3975cc5/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3ad2a3decf9aaba3d29c8f537ac4b243e36bef957511b4766cb0057d32b0be85", size = 762952, upload-time = "2024-08-06T20:32:14.124Z" },
            { url = "https://files.pythonhosted.org/packages/9b/97/ecc1abf4a823f5ac61941a9c00fe501b02ac3ab0e373c3857f7d4b83e2b6/PyYAML-6.0.2-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:ff3824dc5261f50c9b0dfb3be22b4567a6f938ccce4587b38952d85fd9e9afe4", size = 735301, upload-time = "2024-08-06T20:32:16.17Z" },
            { url = "https://files.pythonhosted.org/packages/45/73/0f49dacd6e82c9430e46f4a027baa4ca205e8b0a9dce1397f44edc23559d/PyYAML-6.0.2-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:797b4f722ffa07cc8d62053e4cff1486fa6dc094105d13fea7b1de7d8bf71c9e", size = 756638, upload-time = "2024-08-06T20:32:18.555Z" },
            { url = "https://files.pythonhosted.org/packages/22/5f/956f0f9fc65223a58fbc14459bf34b4cc48dec52e00535c79b8db361aabd/PyYAML-6.0.2-cp311-cp311-win32.whl", hash = "sha256:11d8f3dd2b9c1207dcaf2ee0bbbfd5991f571186ec9cc78427ba5bd32afae4b5", size = 143850, upload-time = "2024-08-06T20:32:19.889Z" },
            { url = "https://files.pythonhosted.org/packages/ed/23/8da0bbe2ab9dcdd11f4f4557ccaf95c10b9811b13ecced089d43ce59c3c8/PyYAML-6.0.2-cp311-cp311-win_amd64.whl", hash = "sha256:e10ce637b18caea04431ce14fabcf5c64a1c61ec9c56b071a4b7ca131ca52d44", size = 161980, upload-time = "2024-08-06T20:32:21.273Z" },
            { url = "https://files.pythonhosted.org/packages/86/0c/c581167fc46d6d6d7ddcfb8c843a4de25bdd27e4466938109ca68492292c/PyYAML-6.0.2-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:c70c95198c015b85feafc136515252a261a84561b7b1d51e3384e0655ddf25ab", size = 183873, upload-time = "2024-08-06T20:32:25.131Z" },
            { url = "https://files.pythonhosted.org/packages/a8/0c/38374f5bb272c051e2a69281d71cba6fdb983413e6758b84482905e29a5d/PyYAML-6.0.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:ce826d6ef20b1bc864f0a68340c8b3287705cae2f8b4b1d932177dcc76721725", size = 173302, upload-time = "2024-08-06T20:32:26.511Z" },
            { url = "https://files.pythonhosted.org/packages/c3/93/9916574aa8c00aa06bbac729972eb1071d002b8e158bd0e83a3b9a20a1f7/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1f71ea527786de97d1a0cc0eacd1defc0985dcf6b3f17bb77dcfc8c34bec4dc5", size = 739154, upload-time = "2024-08-06T20:32:28.363Z" },
            { url = "https://files.pythonhosted.org/packages/95/0f/b8938f1cbd09739c6da569d172531567dbcc9789e0029aa070856f123984/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:9b22676e8097e9e22e36d6b7bda33190d0d400f345f23d4065d48f4ca7ae0425", size = 766223, upload-time = "2024-08-06T20:32:30.058Z" },
            { url = "https://files.pythonhosted.org/packages/b9/2b/614b4752f2e127db5cc206abc23a8c19678e92b23c3db30fc86ab731d3bd/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:80bab7bfc629882493af4aa31a4cfa43a4c57c83813253626916b8c7ada83476", size = 767542, upload-time = "2024-08-06T20:32:31.881Z" },
            { url = "https://files.pythonhosted.org/packages/d4/00/dd137d5bcc7efea1836d6264f049359861cf548469d18da90cd8216cf05f/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:0833f8694549e586547b576dcfaba4a6b55b9e96098b36cdc7ebefe667dfed48", size = 731164, upload-time = "2024-08-06T20:32:37.083Z" },
            { url = "https://files.pythonhosted.org/packages/c9/1f/4f998c900485e5c0ef43838363ba4a9723ac0ad73a9dc42068b12aaba4e4/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:8b9c7197f7cb2738065c481a0461e50ad02f18c78cd75775628afb4d7137fb3b", size = 756611, upload-time = "2024-08-06T20:32:38.898Z" },
            { url = "https://files.pythonhosted.org/packages/df/d1/f5a275fdb252768b7a11ec63585bc38d0e87c9e05668a139fea92b80634c/PyYAML-6.0.2-cp312-cp312-win32.whl", hash = "sha256:ef6107725bd54b262d6dedcc2af448a266975032bc85ef0172c5f059da6325b4", size = 140591, upload-time = "2024-08-06T20:32:40.241Z" },
            { url = "https://files.pythonhosted.org/packages/0c/e8/4f648c598b17c3d06e8753d7d13d57542b30d56e6c2dedf9c331ae56312e/PyYAML-6.0.2-cp312-cp312-win_amd64.whl", hash = "sha256:7e7401d0de89a9a855c839bc697c079a4af81cf878373abd7dc625847d25cbd8", size = 156338, upload-time = "2024-08-06T20:32:41.93Z" },
            { url = "https://files.pythonhosted.org/packages/ef/e3/3af305b830494fa85d95f6d95ef7fa73f2ee1cc8ef5b495c7c3269fb835f/PyYAML-6.0.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:efdca5630322a10774e8e98e1af481aad470dd62c3170801852d752aa7a783ba", size = 181309, upload-time = "2024-08-06T20:32:43.4Z" },
            { url = "https://files.pythonhosted.org/packages/45/9f/3b1c20a0b7a3200524eb0076cc027a970d320bd3a6592873c85c92a08731/PyYAML-6.0.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:50187695423ffe49e2deacb8cd10510bc361faac997de9efef88badc3bb9e2d1", size = 171679, upload-time = "2024-08-06T20:32:44.801Z" },
            { url = "https://files.pythonhosted.org/packages/7c/9a/337322f27005c33bcb656c655fa78325b730324c78620e8328ae28b64d0c/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0ffe8360bab4910ef1b9e87fb812d8bc0a308b0d0eef8c8f44e0254ab3b07133", size = 733428, upload-time = "2024-08-06T20:32:46.432Z" },
            { url = "https://files.pythonhosted.org/packages/a3/69/864fbe19e6c18ea3cc196cbe5d392175b4cf3d5d0ac1403ec3f2d237ebb5/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:17e311b6c678207928d649faa7cb0d7b4c26a0ba73d41e99c4fff6b6c3276484", size = 763361, upload-time = "2024-08-06T20:32:51.188Z" },
            { url = "https://files.pythonhosted.org/packages/04/24/b7721e4845c2f162d26f50521b825fb061bc0a5afcf9a386840f23ea19fa/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:70b189594dbe54f75ab3a1acec5f1e3faa7e8cf2f1e08d9b561cb41b845f69d5", size = 759523, upload-time = "2024-08-06T20:32:53.019Z" },
            { url = "https://files.pythonhosted.org/packages/2b/b2/e3234f59ba06559c6ff63c4e10baea10e5e7df868092bf9ab40e5b9c56b6/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:41e4e3953a79407c794916fa277a82531dd93aad34e29c2a514c2c0c5fe971cc", size = 726660, upload-time = "2024-08-06T20:32:54.708Z" },
            { url = "https://files.pythonhosted.org/packages/fe/0f/25911a9f080464c59fab9027482f822b86bf0608957a5fcc6eaac85aa515/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:68ccc6023a3400877818152ad9a1033e3db8625d899c72eacb5a668902e4d652", size = 751597, upload-time = "2024-08-06T20:32:56.985Z" },
            { url = "https://files.pythonhosted.org/packages/14/0d/e2c3b43bbce3cf6bd97c840b46088a3031085179e596d4929729d8d68270/PyYAML-6.0.2-cp313-cp313-win32.whl", hash = "sha256:bc2fa7c6b47d6bc618dd7fb02ef6fdedb1090ec036abab80d4681424b84c1183", size = 140527, upload-time = "2024-08-06T20:33:03.001Z" },
            { url = "https://files.pythonhosted.org/packages/fa/de/02b54f42487e3d3c6efb3f89428677074ca7bf43aae402517bc7cca949f3/PyYAML-6.0.2-cp313-cp313-win_amd64.whl", hash = "sha256:8388ee1976c416731879ac16da0aff3f63b286ffdd57cdeb95f3f2e085687563", size = 156446, upload-time = "2024-08-06T20:33:04.33Z" },
            { url = "https://files.pythonhosted.org/packages/65/d8/b7a1db13636d7fb7d4ff431593c510c8b8fca920ade06ca8ef20015493c5/PyYAML-6.0.2-cp39-cp39-macosx_10_9_x86_64.whl", hash = "sha256:688ba32a1cffef67fd2e9398a2efebaea461578b0923624778664cc1c914db5d", size = 184777, upload-time = "2024-08-06T20:33:25.896Z" },
            { url = "https://files.pythonhosted.org/packages/0a/02/6ec546cd45143fdf9840b2c6be8d875116a64076218b61d68e12548e5839/PyYAML-6.0.2-cp39-cp39-macosx_11_0_arm64.whl", hash = "sha256:a8786accb172bd8afb8be14490a16625cbc387036876ab6ba70912730faf8e1f", size = 172318, upload-time = "2024-08-06T20:33:27.212Z" },
            { url = "https://files.pythonhosted.org/packages/0e/9a/8cc68be846c972bda34f6c2a93abb644fb2476f4dcc924d52175786932c9/PyYAML-6.0.2-cp39-cp39-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d8e03406cac8513435335dbab54c0d385e4a49e4945d2909a581c83647ca0290", size = 720891, upload-time = "2024-08-06T20:33:28.974Z" },
            { url = "https://files.pythonhosted.org/packages/e9/6c/6e1b7f40181bc4805e2e07f4abc10a88ce4648e7e95ff1abe4ae4014a9b2/PyYAML-6.0.2-cp39-cp39-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f753120cb8181e736c57ef7636e83f31b9c0d1722c516f7e86cf15b7aa57ff12", size = 722614, upload-time = "2024-08-06T20:33:34.157Z" },
            { url = "https://files.pythonhosted.org/packages/3d/32/e7bd8535d22ea2874cef6a81021ba019474ace0d13a4819c2a4bce79bd6a/PyYAML-6.0.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3b1fdb9dc17f5a7677423d508ab4f243a726dea51fa5e70992e59a7411c89d19", size = 737360, upload-time = "2024-08-06T20:33:35.84Z" },
            { url = "https://files.pythonhosted.org/packages/d7/12/7322c1e30b9be969670b672573d45479edef72c9a0deac3bb2868f5d7469/PyYAML-6.0.2-cp39-cp39-musllinux_1_1_aarch64.whl", hash = "sha256:0b69e4ce7a131fe56b7e4d770c67429700908fc0752af059838b1cfb41960e4e", size = 699006, upload-time = "2024-08-06T20:33:37.501Z" },
            { url = "https://files.pythonhosted.org/packages/82/72/04fcad41ca56491995076630c3ec1e834be241664c0c09a64c9a2589b507/PyYAML-6.0.2-cp39-cp39-musllinux_1_1_x86_64.whl", hash = "sha256:a9f8c2e67970f13b16084e04f134610fd1d374bf477b17ec1599185cf611d725", size = 723577, upload-time = "2024-08-06T20:33:39.389Z" },
            { url = "https://files.pythonhosted.org/packages/ed/5e/46168b1f2757f1fcd442bc3029cd8767d88a98c9c05770d8b420948743bb/PyYAML-6.0.2-cp39-cp39-win32.whl", hash = "sha256:6395c297d42274772abc367baaa79683958044e5d3835486c16da75d2a694631", size = 144593, upload-time = "2024-08-06T20:33:46.63Z" },
            { url = "https://files.pythonhosted.org/packages/19/87/5124b1c1f2412bb95c59ec481eaf936cd32f0fe2a7b16b97b81c4c017a6a/PyYAML-6.0.2-cp39-cp39-win_amd64.whl", hash = "sha256:39693e1f8320ae4f43943590b49779ffb98acb81f788220ea932a6b6c51004d8", size = 162312, upload-time = "2024-08-06T20:33:49.073Z" },
        ]

        [[package]]
        name = "rich"
        version = "14.1.0"
        source = { registry = "https://pypi.org/simple" }
        dependencies = [
            { name = "markdown-it-py" },
            { name = "pygments" },
        ]
        sdist = { url = "https://files.pythonhosted.org/packages/fe/75/af448d8e52bf1d8fa6a9d089ca6c07ff4453d86c65c145d0a300bb073b9b/rich-14.1.0.tar.gz", hash = "sha256:e497a48b844b0320d45007cdebfeaeed8db2a4f4bcf49f15e455cfc4af11eaa8", size = 224441, upload-time = "2025-07-25T07:32:58.125Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/e3/30/3c4d035596d3cf444529e0b2953ad0466f6049528a879d27534700580395/rich-14.1.0-py3-none-any.whl", hash = "sha256:536f5f1785986d6dbdea3c75205c473f970777b4a0d6c6dd1b696aa05a3fa04f", size = 243368, upload-time = "2025-07-25T07:32:56.73Z" },
        ]

        [[package]]
        name = "video-splitter"
        version = "1.0.0"
        source = { virtual = "." }
        dependencies = [
            { name = "loguru" },
            { name = "pyyaml" },
            { name = "rich" },
        ]

        [package.metadata]
        requires-dist = [
            { name = "loguru" },
            { name = "pyyaml" },
            { name = "rich" },
        ]

        [[package]]
        name = "win32-setctime"
        version = "1.2.0"
        source = { registry = "https://pypi.org/simple" }
        sdist = { url = "https://files.pythonhosted.org/packages/b3/8f/705086c9d734d3b663af0e9bb3d4de6578d08f46b1b101c2442fd9aecaa2/win32_setctime-1.2.0.tar.gz", hash = "sha256:ae1fdf948f5640aae05c511ade119313fb6a30d7eabe25fef9764dca5873c4c0", size = 4867, upload-time = "2024-12-07T15:28:28.314Z" }
        wheels = [
            { url = "https://files.pythonhosted.org/packages/e1/07/c6fe3ad3e685340704d314d765b7912993bcb8dc198f0e7a89382d37974b/win32_setctime-1.2.0-py3-none-any.whl", hash = "sha256:95d644c4e708aba81dc3704a116d8cbc974d70b3bdb8be1d150e36be6e9d1390", size = 4083, upload-time = "2024-12-07T15:28:26.465Z" },
        ]
    ```

    ---

    #### `src\main.py`

    ```python
        import argparse
        import os
        import subprocess
        import sys
        from concurrent.futures import ThreadPoolExecutor, as_completed
        from dataclasses import dataclass
        from pathlib import Path
        from typing import Dict, List, Optional, Tuple, Any

        import yaml
        from loguru import logger
        from rich.console import Console
        from rich.panel import Panel
        from rich.progress import (Progress, SpinnerColumn, TextColumn,
                                  BarColumn, TimeElapsedColumn)
        from rich.prompt import Prompt, Confirm
        from rich.table import Table

        console = Console()


        class TimecodeParser:
            """Handles normalization and parsing of timecodes."""

            @staticmethod
            def normalize_timecode(time_str: str, require_hours: bool = False) -> str:
                """Enhanced timecode parsing with explicit format detection.

                Formats supported:
                - HH:MM:SS.mmm - Hours:Minutes:Seconds.Milliseconds
                - MM:SS.mmm - Minutes:Seconds.Milliseconds
                - SS.mmm - Seconds.Milliseconds
                - Smart detection for ambiguous cases like 00:17:30
                """
                time_str = time_str.strip()

                # Handle decimal seconds (e.g., "17.30", "1:30.5")
                if '.' in time_str:
                    return TimecodeParser._parse_decimal_timecode(time_str, require_hours)

                if ':' in time_str:
                    parts = time_str.split(':')
                    if require_hours and len(parts) != 3:
                        raise ValueError("Invalid format: use HH:MM:SS.")

                    if len(parts) == 2:
                        # MM:SS format - but check for seconds.milliseconds interpretation
                        minutes_part, seconds_part = parts
                        minutes_val, seconds_val = int(minutes_part), int(seconds_part)

                        # Smart detection: if "seconds" > 59, treat as MM:SS
                        # If both parts are <= 59 and first part has leading zeros, treat as SS.mmm
                        if seconds_val >= 60:
                            # Definitely MM:SS format (seconds > 59)
                            hours = '00'
                            minutes, seconds = minutes_part, seconds_part
                        elif minutes_part == '00' and minutes_val <= 59 and seconds_val <= 99:
                            # Treat as SS.mmm format (e.g., "00:17:30" = 17.30 seconds)
                            hours = '00'
                            minutes = '00'
                            total_seconds = minutes_val + (seconds_val / 100.0)
                            return f"00:00:{total_seconds:06.3f}"
                        else:
                            # Standard MM:SS format
                            hours = '00'
                            minutes, seconds = minutes_part, seconds_part

                    elif len(parts) == 3:
                        hours, minutes, seconds = parts
                        hours_val, minutes_val, seconds_val = int(hours), int(minutes), int(seconds)

                        # Standard HH:MM:SS format
                        hours, minutes, seconds = hours, minutes, seconds

                    elif len(parts) == 1:
                        hours = '00'
                        minutes = '00'
                        seconds = parts[0]
                    else:
                        raise ValueError("Invalid format: HH:MM:SS, MM:SS, or SS expected.")
                else:
                    # Handle numeric-only formats (HHMMSS, MMSS, SS)
                    return TimecodeParser._parse_numeric_timecode(time_str, require_hours)

                # Validate components
                hours, minutes, seconds = int(hours), int(minutes), int(seconds)
                if not (0 <= minutes < 60) or not (0 <= seconds < 60) or hours < 0:
                    raise ValueError("Invalid time components.")

                if require_hours or hours > 0:
                    return f"{hours:02}:{minutes:02}:{seconds:02}"
                elif minutes > 0:
                    return f"{minutes:02}:{seconds:02}"
                else:
                    return f"{seconds:02}"

            @staticmethod
            def _parse_decimal_timecode(time_str: str, require_hours: bool = False) -> str:
                """Parse timecode with decimal seconds (e.g., '17.30', '1:30.5')."""
                if ':' in time_str:
                    # Format like "1:30.5" or "0:17.30"
                    parts = time_str.split(':')
                    if len(parts) == 2:
                        minutes_part, seconds_part = parts
                        minutes = int(minutes_part)
                        seconds = float(seconds_part)
                        total_seconds = minutes * 60 + seconds
                        return f"00:00:{total_seconds:06.3f}"
                    else:
                        raise ValueError("Decimal format supports MM:SS.mmm only.")
                else:
                    # Format like "17.30" (seconds.milliseconds)
                    seconds = float(time_str)
                    return f"00:00:{seconds:06.3f}"

            @staticmethod
            def _parse_numeric_timecode(time_str: str, require_hours: bool = False) -> str:
                """Parse numeric-only timecode (HHMMSS, MMSS, SS)."""
                import re
                digits = ''.join(re.findall(r'\d+', time_str))
                length = len(digits)

                if require_hours:
                    if length != 6:
                        raise ValueError("Invalid length: use HHMMSS.")
                    hours, minutes, seconds = digits[:2], digits[2:4], digits[4:6]
                else:
                    if length == 6:  # HHMMSS
                        hours, minutes, seconds = digits[:2], digits[2:4], digits[4:6]
                    elif length == 4:  # MMSS
                        hours = '00'
                        minutes, seconds = digits[:2], digits[2:4]
                    elif length == 2:  # SS
                        hours = '00'
                        minutes = '00'
                        seconds = digits
                    else:
                        raise ValueError("Invalid length: use HHMMSS, MMSS, or SS.")

                return f"{hours}:{minutes}:{seconds}"

            @staticmethod
            def parse_timecode(time_str: str, require_hours: bool = False) -> float:
                """Parse timecode and return total seconds as float for precision."""
                normalized = TimecodeParser.normalize_timecode(time_str, require_hours)
                parts = normalized.split(':')

                if len(parts) == 3:
                    hours, minutes = int(parts[0]), int(parts[1])
                    # Handle decimal seconds
                    seconds = float(parts[2]) if '.' in parts[2] else int(parts[2])
                elif len(parts) == 2:
                    hours = 0
                    minutes = int(parts[0])
                    seconds = float(parts[1]) if '.' in parts[1] else int(parts[1])
                else:
                    hours = 0
                    minutes = 0
                    seconds = float(parts[0]) if '.' in parts[0] else int(parts[0])

                return hours * 3600 + minutes * 60 + seconds

            @staticmethod
            def format_duration(seconds: float, require_hours: bool = False) -> str:
                hours = int(seconds // 3600)
                minutes = int((seconds % 3600) // 60)
                secs = int(seconds % 60)
                if require_hours or hours > 0:
                    return f"{hours:02}:{minutes:02}:{secs:02}"
                elif minutes > 0:
                    return f"{minutes:02}:{secs:02}"
                else:
                    return f"{secs:02}"

            @staticmethod
            def format_seconds(seconds: int, require_hours: bool = False) -> str:
                return TimecodeParser.format_duration(seconds, require_hours)


        @dataclass
        class VideoSegment:
            """Represents a single video segment."""
            name: str
            start: str
            end: str


        def parse_arguments():
            """Parse command line arguments."""
            parser = argparse.ArgumentParser(description="Split a video into segments using FFmpeg")
            parser.add_argument("-i", "--input_video", type=str, help="Path to input video file")
            parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
            parser.add_argument("-tc", "--timecodes_file", type=str, help="Path to YAML/CSV timecodes file")
            parser.add_argument("--ffmpeg_path", default="ffmpeg", help="Path to ffmpeg executable")
            parser.add_argument("--ffprobe_path", default="ffprobe", help="Path to ffprobe executable")
            parser.add_argument("--output_format", default="mp4", help="Output video format")
            parser.add_argument("--codec", default="copy", help="Video codec (copy, libx264, etc.)")
            parser.add_argument("--threads", type=int, default=1, help="Number of concurrent threads")
            parser.add_argument("--after_effects_compatible", action="store_true",
                                help="Re-encode for Adobe After Effects compatibility")
            parser.add_argument("--append_timestamps", action="store_true", default=True,
                                help="Append timestamps to output filenames")
            parser.add_argument("--include_source_prefix", action="store_true", default=False,
                                help="Include source filename as prefix")
            parser.add_argument("--use_subdirectory", action="store_true", default=False,
                                help="Create subdirectory for output files")
            parser.add_argument("--prompt", action="store_true", help="Interactive mode")
            return parser.parse_args()

        def setup_logger():
            """Initialize logging with loguru."""
            logger.remove()
            logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
            logger.add("video_splitter.log", level="DEBUG", format="{time} - {level} - {message}")

        def cleanup_logs():
            """Clean up log files after successful execution."""
            import time
            log_file = "video_splitter.log"

            # Remove all logger handlers to release file handles
            logger.remove()

            # Give the system a moment to release the file handle (Windows issue)
            time.sleep(0.1)

            if os.path.exists(log_file):
                # Try multiple times with increasing delays
                for attempt in range(3):
                    try:
                        os.remove(log_file)
                        console.print("[bold green]Successfully cleaned up log file[/bold green]")
                        return
                    except PermissionError:
                        if attempt < 2:  # Not the last attempt
                            time.sleep(0.2 * (attempt + 1))  # 0.2s, 0.4s delays
                            continue
                        else:
                            # On final attempt, just silently skip cleanup
                            console.print("[dim]Log file cleanup skipped (file in use)[/dim]")
                            return
                    except Exception as e:
                        console.print(f"[bold red]Error cleaning up log file: {str(e)}[/bold red]")
                        return

        def wait_for_user_exit():
            """Wait for user input before exiting."""
            console.print("\n[dim]Press Enter to exit...[/dim]")
            input()

        def get_video_duration(video_path: str, ffprobe_path: str = "ffprobe") -> float:
            """Get video duration using ffprobe."""
            cmd = [
                ffprobe_path, "-v", "error", "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1", video_path
            ]
            try:
                result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
                return float(result.stdout.decode().strip())
            except subprocess.CalledProcessError as e:
                raise RuntimeError(f"Failed to get video duration: {e.stderr.decode().strip()}")

        def load_timecodes_from_yaml(file_path: str) -> Dict[str, Any]:
            """Load configuration and segments from YAML file."""
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f) or {}

            segments = []
            for seg in data.get('segments', []):
                if seg.get('start') and seg.get('end') and (seg.get('title') or seg.get('name')):
                    segments.append((
                        seg['start'].strip(),
                        seg['end'].strip(),
                        (seg.get('title') or seg.get('name')).strip()
                    ))

            return {
                'input_video': data.get('input_video'),
                'output_dir': data.get('output_dir'),
                'segments': segments,
                'ffmpeg_path': data.get('ffmpeg_path', 'ffmpeg'),
                'ffprobe_path': data.get('ffprobe_path', 'ffprobe'),
                'output_format': data.get('output_format', 'mp4'),
                'codec': data.get('codec', 'copy'),
                'threads': data.get('threads', 1),
                'after_effects_compatible': data.get('after_effects_compatible', False),
                'append_timestamps': data.get('append_timestamps', True),
                'include_source_prefix': data.get('include_source_prefix', False),
                'use_subdirectory': data.get('use_subdirectory', False)
            }

        def prompt_for_segments(video_duration: float) -> List[Tuple[str, str, str]]:
            """Interactively prompt user for video segments with validation."""
            formatted_dur = TimecodeParser.format_duration(video_duration)
            require_hours = video_duration >= 3600
            fmts = ("HH:MM:SS, MM:SS, SS, HHMMSS, MMSS" if require_hours
                    else "MM:SS, SS, MMSS")

            console.print(f"\n[bold cyan]Video Duration:[/bold cyan] [dim]{formatted_dur}[/dim] | "
                          f"[bold cyan]Formats:[/bold cyan] [dim]{fmts}[/dim]\n")
            console.print("[bold cyan]Enter segments:[/bold cyan]\n")

            result = []
            idx = 1

            while True:
                name = Prompt.ask(f"[bold cyan]Segment {idx} name[/bold cyan]",
                                 default=f"clip{idx}")

                # Get start time with validation
                start_sec = None
                while start_sec is None:
                    st = Prompt.ask("[bold cyan]Start time[/bold cyan]")
                    try:
                        start_sec = TimecodeParser.parse_timecode(st, require_hours)
                    except ValueError as v:
                        console.print(f"[bold red]{v}[/bold red]")

                # Get end time with validation
                end_sec = None
                while end_sec is None:
                    en = Prompt.ask("[bold cyan]End time[/bold cyan]")
                    try:
                        en_sec = TimecodeParser.parse_timecode(en, require_hours)
                        if en_sec <= start_sec:
                            raise ValueError("End must be greater than start.")
                        if en_sec > video_duration:
                            raise ValueError("End exceeds video duration.")
                        end_sec = en_sec
                    except ValueError as v:
                        console.print(f"[bold red]{v}[/bold red]")

                # Format and add segment
                st_form = TimecodeParser.format_seconds(start_sec, require_hours)
                en_form = TimecodeParser.format_seconds(end_sec, require_hours)
                result.append((st_form, en_form, name))
                console.print(f"[bold green]Added '{name}': {st_form} to {en_form}[/bold green]\n")
                idx += 1

                if not Confirm.ask("[bold cyan]Add another segment?[/bold cyan]", default=False):
                    break

            return result


        def discover_and_select_timecode_file() -> Optional[str]:
            """Discover available timecode files and let user select interactively."""
            timecodes_dir = Path("timecodes")

            if not timecodes_dir.exists():
                console.print(f"[bold yellow]Timecodes directory '{timecodes_dir}' not found[/bold yellow]")
                return None

            # Find YAML files in timecodes directory
            yaml_files = list(timecodes_dir.glob("*.yaml")) + list(timecodes_dir.glob("*.yml"))

            if not yaml_files:
                console.print(f"[bold yellow]No YAML timecode files found in '{timecodes_dir}'[/bold yellow]")
                return None

            # Display available files in a table
            console.print(f"\n[bold cyan]Available timecode files in '{timecodes_dir}':[/bold cyan]")
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("No.", style="cyan", width=4)
            table.add_column("Filename", style="green")
            table.add_column("Size", style="dim", width=8)

            for idx, file_path in enumerate(yaml_files, 1):
                file_size = f"{file_path.stat().st_size}B" if file_path.exists() else "N/A"
                table.add_row(str(idx), file_path.name, file_size)

            console.print(table)

            # Prompt for selection
            while True:
                try:
                    choice = Prompt.ask(
                        f"[bold cyan]Select timecode file (1-{len(yaml_files)}) or 'q' to skip[/bold cyan]",
                        default="q"
                    )

                    if choice.lower() == 'q':
                        return None

                    file_idx = int(choice) - 1
                    if 0 <= file_idx < len(yaml_files):
                        selected_file = str(yaml_files[file_idx])
                        console.print(f"[bold green]Selected: {selected_file}[/bold green]")
                        return selected_file
                    else:
                        console.print(f"[bold red]Invalid selection. Choose 1-{len(yaml_files)} or 'q'[/bold red]")
                except ValueError:
                    console.print(f"[bold red]Invalid input. Enter a number (1-{len(yaml_files)}) or 'q'[/bold red]")


        def check_output_directory(output_dir: str):
            """Check if output directory exists and create if needed."""
            if not Path(output_dir).exists():
                if Confirm.ask(f"[bold cyan]Create output directory '{output_dir}'?[/bold cyan]",
                              default=True):
                    try:
                        os.makedirs(output_dir, exist_ok=True)
                        console.print(f"[bold green]Created directory: {output_dir}[/bold green]\n")
                    except Exception as e:
                        console.print(f"[bold red]Error creating directory: {e}[/bold red]")
                        sys.exit(1)
                else:
                    console.print("[yellow]Operation cancelled by user[/yellow]")
                    sys.exit(1)


        def get_user_inputs(args):
            """Get and validate user inputs, with interactive prompts if needed."""
            yaml_config = {}

            # Load YAML configuration if provided or prompt for it
            if args.prompt and not args.timecodes_file:
                if Confirm.ask("[bold cyan]Load a YAML/CSV timecodes file?[/bold cyan]",
                              default=True):
                    while True:
                        timecodes_path = Prompt.ask("[bold cyan]Path to timecodes file (or press Enter to browse)[/bold cyan]",
                                                  default="")

                        # If user pressed Enter (empty input), show file browser
                        if not timecodes_path.strip():
                            selected_file = discover_and_select_timecode_file()
                            if selected_file:
                                args.timecodes_file = selected_file
                            break  # Exit loop whether file was selected or user chose to skip

                        # User entered a specific path
                        if Path(timecodes_path).is_file():
                            args.timecodes_file = timecodes_path
                            break
                        else:
                            console.print(f"[bold red]File not found: {timecodes_path}[/bold red]")
                            # Continue the loop to retry - no discovery here

            if args.timecodes_file and Path(args.timecodes_file).is_file():
                try:
                    yaml_config = load_timecodes_from_yaml(args.timecodes_file)
                    console.print(f"[bold green]Loaded configuration from {args.timecodes_file}[/bold green]")
                except Exception as e:
                    console.print(f"[bold red]Error loading YAML file: {e}[/bold red]")

            # Prompt for input video
            if args.prompt or not args.input_video:
                default_video = yaml_config.get('input_video') or args.input_video or ""
                args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]",
                                            default=default_video)
                while not Path(args.input_video).is_file():
                    console.print("[bold red]File not found. Try again.[/bold red]")
                    args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]")

            if not args.input_video or not Path(args.input_video).is_file():
                console.print("[bold red]Error: Input video file not found[/bold red]")
                sys.exit(1)

            # Interactive prompts for additional options if in prompt mode
            if args.prompt:
                use_subdir = Confirm.ask(
                    "[bold cyan]Place output in a separate subdirectory?[/bold cyan]",
                    default=yaml_config.get("use_subdirectory", False)
                )
                if use_subdir:
                    args.output_path = Prompt.ask(
                        "[bold cyan]Output directory[/bold cyan]",
                        default=yaml_config.get("output_dir", "output_segments")
                    )
                else:
                    args.output_path = str(Path(args.input_video).parent)

                args.append_timestamps = Confirm.ask(
                    "[bold cyan]Append timestamps to filenames?[/bold cyan]",
                    default=yaml_config.get("append_timestamps", True)
                )
                args.include_source_prefix = Confirm.ask(
                    "[bold cyan]Include source file's name as prefix?[/bold cyan]",
                    default=yaml_config.get("include_source_prefix", False)
                )
                args.after_effects_compatible = Confirm.ask(
                    "[bold cyan]Re-encode segments for Adobe After Effects compatibility?[/bold cyan]",
                    default=yaml_config.get("after_effects_compatible", False)
                )
            elif not args.output_path:
                default_output = yaml_config.get('output_dir') or args.output_path
                if not default_output:
                    if yaml_config.get('use_subdirectory', False):
                        default_output = "output_segments"
                    else:
                        default_output = str(Path(args.input_video).parent)
                args.output_path = default_output

            # Handle segments
            segments = yaml_config.get('segments', [])
            if args.prompt or not segments:
                if segments:
                    console.print(f"[bold green]Found {len(segments)} segments from YAML/CSV.[/bold green]")
                    if args.prompt:
                        use_existing = Confirm.ask("[bold cyan]Use these segments as-is?[/bold cyan]",
                                                 default=True)
                        if not use_existing:
                            discard = Confirm.ask(
                                "[bold cyan]Discard loaded segments and define new ones?[/bold cyan]",
                                default=True
                            )
                            if discard:
                                segments = []

                if not segments:
                    try:
                        duration = get_video_duration(args.input_video,
                                                    yaml_config.get('ffprobe_path', args.ffprobe_path))
                        segments = prompt_for_segments(duration)
                    except Exception as e:
                        console.print(f"[bold red]Error getting video duration: {e}[/bold red]")
                        sys.exit(1)

            # Merge YAML config with CLI args (CLI takes precedence)
            for key, value in yaml_config.items():
                if not hasattr(args, key) or getattr(args, key) is None:
                    setattr(args, key, value)

            return args.input_video, args.output_path, segments, args

        def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str,
                               ffmpeg_path: str, output_format: str, codec: str,
                               append_timestamps: bool, include_source_prefix: bool,
                               after_effects_compatible: bool) -> Tuple[str, Optional[str], Optional[str]]:
            """Split a single video segment using FFmpeg."""

            source_name = Path(input_video).stem if include_source_prefix else ""
            prefix = f"{source_name}_" if include_source_prefix else ""

            if append_timestamps:
                start_clean = segment.start.replace(':', '-')
                end_clean = segment.end.replace(':', '-')
                filename = f"{prefix}{segment.name}_{start_clean}_to_{end_clean}.{output_format}"
            else:
                filename = f"{prefix}{segment.name}.{output_format}"

            output_path = Path(output_dir) / filename

            if after_effects_compatible:
                cmd = [
                    ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
                    "-c:v", "libx264", "-preset", "faster", "-pix_fmt", "yuv420p",
                    "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart", "-y", str(output_path)
                ]
            else:
                # Optimized settings for video editing compatibility and frame-accurate playback
                if codec == "copy":
                    # Stream copy - fast but may have seeking issues
                    cmd = [
                        ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
                        "-c", "copy", "-avoid_negative_ts", "make_zero", "-y", str(output_path)
                    ]
                else:
                    # Corrected optimal settings for video editing workflows
                    cmd = [
                        ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
                        "-c:v", codec, "-preset", "medium", "-crf", "18", "-g", "6", "-bf", "0",
                        "-pix_fmt", "yuv420p", "-colorspace", "bt709", "-color_primaries", "bt709",
                        "-color_trc", "bt709", "-color_range", "mpeg",
                        "-c:a", "aac", "-b:a", "192k", "-ar", "48000",
                        "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
                        "-y", str(output_path)
                    ]



            try:
                subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                return segment.name, str(output_path), None
            except subprocess.CalledProcessError as e:
                return segment.name, None, e.stderr.decode().strip()


        def process_video_segments(input_video: str, output_dir: str,
                                   segments: List[Tuple[str, str, str]],
                                   config: argparse.Namespace) -> List[Tuple[str, str]]:
            """Process all video segments with progress tracking."""
            Path(output_dir).mkdir(parents=True, exist_ok=True)

            segment_objects = [VideoSegment(name, start, end)
                               for start, end, name in segments]
            results = []

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
                console=console,
                transient=True
            ) as progress:
                task = progress.add_task(
                    "[bold cyan]Processing video segments...[/bold cyan]",
                    total=len(segment_objects)
                )

                with ThreadPoolExecutor(max_workers=config.threads) as executor:
                    future_to_segment = {
                        executor.submit(
                            split_video_segment,
                            input_video,
                            segment,
                            output_dir,
                            config.ffmpeg_path,
                            config.output_format,
                            config.codec,
                            config.append_timestamps,
                            config.include_source_prefix,
                            config.after_effects_compatible
                        ): segment.name for segment in segment_objects
                    }

                    for future in as_completed(future_to_segment):
                        segment_name = future_to_segment[future]
                        try:
                            name, output_path, error = future.result()
                            if output_path:
                                results.append((name, output_path))
                                console.print(f"[bold green]✓ Completed: {name}[/bold green]")
                            else:
                                results.append((name, f"Failed: {error}"))
                                console.print(f"[bold red]✗ Failed: {name}[/bold red]")
                        except Exception as e:
                            error_msg = f"Exception: {str(e)}"
                            results.append((segment_name, error_msg))
                            console.print(f"[bold red]✗ Exception: {segment_name}[/bold red]")
                        finally:
                            progress.advance(task)

            return results

        def display_summary(input_video: str, output_dir: str,
                            segments: List[Tuple[str, str, str]],
                            config: argparse.Namespace):
            """Display operation summary before processing."""
            table = Table(title="Video Splitting Configuration",
                          show_header=False, box=None)
            table.add_column("Parameter", style="bold cyan")
            table.add_column("Value", style="dim")

            table.add_row("Input Video", input_video)
            table.add_row("Output Directory", output_dir)
            table.add_row("Output Format", config.output_format)
            table.add_row("Codec", config.codec)
            table.add_row("Threads", str(config.threads))
            ae_compat = "Yes" if config.after_effects_compatible else "No"
            table.add_row("AE Compatible", ae_compat)
            append_ts = "Yes" if config.append_timestamps else "No"
            table.add_row("Append Timestamps", append_ts)
            include_prefix = "Yes" if config.include_source_prefix else "No"
            table.add_row("Include Source Prefix", include_prefix)
            table.add_row("Segments", str(len(segments)))

            console.print("\n[bold blue]--- Operation Summary ---[/bold blue]")
            console.print(Panel(table, border_style="blue"))

            if segments:
                console.print("\n[bold cyan]Segments to process:[/bold cyan]")
                seg_table = Table(show_header=True, header_style="bold magenta")
                seg_table.add_column("Name", style="cyan")
                seg_table.add_column("Start", style="green")
                seg_table.add_column("End", style="green")

                for start, end, name in segments:
                    seg_table.add_row(name, start, end)
                console.print(seg_table)


        def display_results(results: List[Tuple[str, str]]):
            """Display processing results."""
            table = Table(title="Processing Results", show_header=True,
                          header_style="bold cyan")
            table.add_column("Segment", style="cyan")
            table.add_column("Status", style="dim")
            table.add_column("Output", style="dim")

            for name, result in results:
                if result.startswith("Failed") or result.startswith("Exception"):
                    table.add_row(name, "[bold red]Failed[/bold red]", result)
                else:
                    table.add_row(name, "[bold green]Success[/bold green]", result)

            console.print("\n")
            console.print(table)


        def main():
            """Main entry point."""
            args = parse_arguments()
            input_video, output_path, segments, config = get_user_inputs(args)

            setup_logger()

            try:
                # Check and create output directory
                check_output_directory(output_path)

                # Display summary and get confirmation
                display_summary(input_video, output_path, segments, config)

                proceed_msg = "\n[bold cyan]Proceed with video splitting?[/bold cyan]"
                if not Confirm.ask(proceed_msg, default=True):
                    console.print("[yellow]Operation cancelled by user[/yellow]")
                    return

                # Process video segments
                console.print("\n[bold cyan]Starting video processing...[/bold cyan]")
                results = process_video_segments(input_video, output_path,
                                                 segments, config)

                # Display results
                display_results(results)

                # Check if all segments were successful
                def is_failed(result):
                    return (result.startswith("Failed") or
                            result.startswith("Exception"))

                successful = all(not is_failed(result) for _, result in results)

                if successful:
                    success_msg = ("\n[bold green]All segments processed "
                                   "successfully![/bold green]")
                    console.print(success_msg)
                    cleanup_logs()
                else:
                    fail_msg = ("\n[bold yellow]Some segments failed to process. "
                                "Check the results above.[/bold yellow]")
                    console.print(fail_msg)

            except Exception as e:
                console.print(f"\n[bold red]Error:[/bold red] {e}")
                logger.exception(f"Main execution error: {e}")
            finally:
                console.print("\n[bold green]Video splitting completed.[/bold green]")
                wait_for_user_exit()




        if __name__ == "__main__":
            main()
    ```

    ---

    #### `timecodes\2025.01.22-kl.10.49.34--Perspektiv_merged_compressed-timecodes.yaml`

    ```yaml
        # input-timecodes.yaml

        # 2025.01.22-kl.10.49.34--Perspektiv_merged_compressed

        # input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
        # use_subdirectory: true
        # output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv"

        input_video: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
        use_subdirectory: true
        output_dir: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter"

        ffmpeg_path: "ffmpeg"
        ffprobe_path: "ffprobe"
        output_format: "mp4"
        codec: "copy"
        threads: 4
        after_effects_compatible: false
        append_timestamps: true
        include_source_prefix: false

        segments:
          - start: "00:06"
            end: "00:17"
            title: "a-starte videoopptak"

          # - start: "00:34"
          #   end: "04:44"
          #   title: "b-dele perspektiv"

          # - start: "04:54:00"
          #   end: "06:43:20"
          #   title: "c-det uperfekte, hånd i hånd"
          #   # title: "c-vi står hånd i hånd"

          - start: "07:01:16"
            end: "09:39:12"
            title: "d-oppmerksomheten vår"


        # log_file: "split_video.log"
        # verbose: true
        # prompt: false
    ```

    ---

    #### `timecodes\input-timecodes.yaml`

    ```yaml
        # input-timecodes.yaml
        # Sample configuration file for Video Splitter

        input_video: "sample_video.mp4"
        use_subdirectory: true
        output_dir: "output_segments"

        ffmpeg_path: "ffmpeg"
        ffprobe_path: "ffprobe"
        output_format: "mp4"
        codec: "libx264"  # Use "libx264" for frame-accurate seeking, "copy" for speed
        threads: 4
        after_effects_compatible: false
        append_timestamps: true
        include_source_prefix: true

        # TIMECODE FORMAT GUIDE:
        # Use explicit H:MM:SS.mmm format to avoid ambiguity
        # Examples:
        #   0:00:17.30  = 17.30 seconds
        #   0:04:41.14  = 4 minutes 41.14 seconds
        #   1:30:45.00  = 1 hour 30 minutes 45 seconds

        segments:
          - start: "0:00:46.00"
            end: "0:01:16.00"
            title: "Introduction"

          - start: "0:01:17.00"
            end: "0:05:17.00"
            title: "Main Content"

          - start: "0:08:21.00"
            end: "0:11:12.00"
            title: "Conclusion"
    ```


<!-- ======================================================= -->
<!-- [2025.08.05 23:51] -->

The optimal FFmpeg settings for a video editing workflow prioritize **quality, frame accuracy, and editing performance** over file size. The goal is to create intermediate files, often called mezzanine or proxy files, that are easy for editing software like Adobe Premiere Pro or DaVinci Resolve to handle. The script already contains an excellent, albeit hidden, foundation for this in its re-encoding `else` block. The key is to move away from `codec: copy` for anything requiring precise editing.

    Here are the optimal settings and the research topics that explain the reasoning behind them.

    -----

    ## Optimal FFmpeg Settings for Editing

    For the best experience in a video editor, you should re-encode your clips to a format that is less compressed and uses a simple frame structure.

    ### Recommendation 1: High-Quality H.264 (All-Intra)

    This is a great balance of quality, compatibility, and file size. It's a significant improvement over standard H.264 for editing.

    ```bash
    ffmpeg -i "input.mp4" -ss [start] -to [end] \
    -c:v libx264 -preset medium -crf 18 -g 1 \
    -c:a pcm_s16le \
    -pix_fmt yuv420p -color_primaries bt709 -color_trc bt709 -color_range tv \
    -movflags +faststart \
    -y "output.mp4"
    ```

      * **`-c:v libx264`**: The video codec.
      * **`-crf 18`**: **Visually lossless quality**. A Constant Rate Factor (CRF) between 17 and 18 is generally considered visually indistinguishable from the source for H.264.
      * **`-g 1`**: This is critical. It sets the keyframe interval to 1, meaning **every frame is a keyframe (an I-frame)**. This is known as an "All-Intra" codec. It dramatically improves seeking performance in editors but increases file size. Your script's default of `-g 6` is good, but `-g 1` is ideal for editing.
      * **`-c:a pcm_s16le`**: Use uncompressed audio (`PCM`). This avoids a second layer of audio compression and potential quality loss. It's the standard for professional video formats. The output container should be `.mov` for this.
      * **Color Flags**: The flags you're already using (`-pix_fmt`, `-color_primaries`, etc.) are **excellent**. They prevent color shifts between players and editors.

    ### Recommendation 2: Professional Mezzanine Codec (ProRes)

    This is the industry-standard choice for editing on both macOS and Windows. It creates large files but offers the best performance and quality preservation during the editing process.

    ```bash
    ffmpeg -i "input.mp4" -ss [start] -to [end] \
    -c:v prores_ks -profile:v 3 \
    -c:a pcm_s16le \
    -vendor ap10 \
    -y "output.mov"
    ```

      * **`-c:v prores_ks`**: Use the high-quality, standard ProRes codec.
      * **`-profile:v 3`**: Corresponds to ProRes 422 (HQ). A great choice for high-quality source material. Other profiles exist for different needs (e.g., `2` for standard ProRes 422).
      * **`-c:a pcm_s16le`**: Again, uncompressed audio is best.
      * **Container**: ProRes should always be in a `.mov` container.

    -----

    ## Research Topics for Optimal Video Exports

    To understand *why* these settings are optimal, consider these topics.

    ### 🎬 Topic 1: Intermediate vs. Delivery Codecs

    This is the most important concept. The files you create with your `video_splitter` script are **intermediate** files, not final delivery files.

      * **Delivery Codecs (e.g., standard H.264, H.265/HEVC, VP9):** These are highly compressed to create small file sizes for streaming (YouTube, Netflix) or sharing. They are difficult for editing software to decode on the fly.
      * **Intermediate Codecs (e.g., ProRes, DNxHD, All-Intra H.264):** These are lightly compressed or uncompressed. They prioritize fast decoding and easy seeking for a smooth editing experience. The file sizes are much larger. Your goal is to convert from a delivery format to an intermediate one.

    ### 🎞️ Topic 2: Frame Accuracy and GOP (Group of Pictures)

    This explains why `codec: copy` is fast but bad for precise editing.

    Most videos use a **Group of Pictures (GOP)** to save space. A GOP consists of:

      * **I-frame (Intra-coded):** A full, self-contained frame. Like a JPEG image.
      * **P-frame (Predicted):** A partial frame that only stores the *changes* from the previous I-frame or P-frame.
      * **B-frame (Bi-directional):** A partial frame that stores changes from *both* previous and future frames.

    When you use `-c copy`, FFmpeg can only make cuts on **I-frames**. If your timecode falls on a P-frame, FFmpeg will cut at the *previous* I-frame, resulting in a clip that starts earlier than you intended.

    **Solution:** Re-encoding to an **All-Intra** format (like ProRes or H.264 with `-g 1`) makes every single frame an I-frame. This guarantees your cuts are frame-accurate and makes scrubbing through the timeline in your editor incredibly fast.

    ### 🎨 Topic 3: Color Space and Color Range

    Your script already correctly includes flags for this, which is great\! This topic prevents the common problem of "my video looks washed out/dark in my editor."

      * **Color Space (`-colorspace`, `-color_primaries`, `-color_trc`):** These flags tag the video with color information. `bt.709` is the standard for all modern HD video. Explicitly tagging it ensures the editor interprets the colors correctly.
      * **Color Range (`-color_range`):** Video can have two ranges:
          * **`tv` (Limited Range):** The standard for broadcast (values 16-235). This is what most cameras record.
          * **`pc` (Full Range):** Used by computers and graphics (values 0-255).
            A mismatch between the source and the editor's interpretation causes colors to look crushed (too dark) or washed out. By setting `-color_range tv` (or `mpeg`), you force the correct interpretation.

    ### 💡 Recommendations for Your Script

    1.  **Modify the `after_effects_compatible` flag.** Rename it to something more general like `editing_profile` or `mezzanine`.
    2.  **Enhance the Codec Logic.** Instead of just `copy` vs. `re-encode`, you could offer profiles in your YAML file:
        ```yaml
        # codec_profile: "fast_copy" # Uses -c copy
        # codec_profile: "h264_intra"  # Uses the recommended H.264 All-Intra settings
        codec_profile: "prores_hq"   # Uses ProRes 422 HQ
        ```
    3.  **Update the `split_video_segment` Function.** Add logic to build the FFmpeg command based on the selected `codec_profile`.


<!-- ======================================================= -->
<!-- [2025.08.06 00:09] -->

have you remembered to account for the correct interpretation of the time (minutes/seconds), and additionally-how does your solution compare to tjis?

```bash
ffmpeg -i input.mp4
  -ss <start> -to <end>
  -c:v prores_ks -profile:v 3 -pix_fmt yuv422p10le -vendor apl0
  -bits_per_mb 8000 -g 30 -bf 0
  -colorspace bt709 -color_primaries bt709 -color_trc bt709
  -c:a pcm_s24le -ar 48000
  -vsync cfr -avoid_negative_ts make_zero
  output_edit.mov
```