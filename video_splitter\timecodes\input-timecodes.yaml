# input-timecodes.yaml
# Sample configuration file for Video Splitter

input_video: "sample_video.mp4"
use_subdirectory: true
output_dir: "output_segments"

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"
output_format: "mp4"

# ENCODING PROFILE OPTIONS:
# fast_copy    - Stream copy (fastest, may have seeking issues)
# h264_intra   - All-Intra H.264 (frame-accurate, editing-optimized)
# prores_hq    - ProRes 422 HQ (professional mezzanine, requires .mov)
# legacy_ae    - Legacy After Effects compatibility
codec_profile: "h264_intra"

# QUALITY CONTROL FOR FILE SIZE
# Quality setting (0-51): Controls file size vs quality trade-off
# - 0: Lossless (largest files, perfect quality)
# - 18: Visually lossless (default, excellent quality)
# - 23: Standard quality (good balance)
# - 28: Smaller files (good quality, noticeable compression)
# - 35: Much smaller files (acceptable quality, visible artifacts)
quality: 18

threads: 4
append_timestamps: true
include_source_prefix: true

# TIMECODE FORMAT GUIDE:
# Use HH:MM:SS or HH:MM:SS.ms format for unambiguous interpretation:
#   "00:00:17"       = 17 seconds
#   "00:01:30"       = 1 minute 30 seconds
#   "00:00:17.500"   = 17.5 seconds (500ms)
#   "00:01:30.250"   = 1 minute 30.25 seconds (250ms)
#   "01:30:45"       = 1 hour 30 minutes 45 seconds

segments:
  - start: "00:00:46"
    end: "00:01:16"
    title: "Introduction"

  - start: "00:01:17"
    end: "00:05:17"
    title: "Main Content"

  - start: "00:08:21"
    end: "00:11:12"
    title: "Conclusion"
