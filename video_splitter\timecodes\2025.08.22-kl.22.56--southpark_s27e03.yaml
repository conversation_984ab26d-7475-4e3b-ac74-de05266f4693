# editing-optimized-example.yaml
# Professional video editing workflow configuration

input_video: "C:/Users/<USER>/Desktop/my/flow/downloads/South Park S27E03 Sickofancy.mkv"
use_subdirectory: true
output_dir: "C:/Users/<USER>/Desktop/my/flow/android/share"

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"

# ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
# ==============================================

# RECOMMENDED: All-Intra H.264 for frame-accurate editing
# - Every frame is a keyframe (I-frame) for precise cuts
# - CRF 18 provides visually lossless quality
# - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
codec_profile: "h264_intra"
output_format: "mp4"

# QUALITY CONTROL FOR FILE SIZE
# =============================
# Quality setting (0-51): Controls file size vs quality trade-off
# - 0: Lossless (largest files, perfect quality)
# - 18: Visually lossless (default, excellent quality)
# - 23: Standard quality (good balance)
# - 28: Smaller files (good quality, noticeable compression)
# - 35: Much smaller files (acceptable quality, visible artifacts)
quality: 18

# ALTERNATIVE: ProRes 422 HQ for professional workflows
# Uncomment the lines below for ProRes output:
# codec_profile: "prores_hq"
# output_format: "mov"  # ProRes requires MOV container

# ALTERNATIVE: Fast stream copy for quick previews (smallest files)
# codec_profile: "fast_copy"
# Note: fast_copy ignores quality setting

# ALTERNATIVE: Legacy After Effects compatibility
# codec_profile: "legacy_ae"

threads: 4
append_timestamps: true
include_source_prefix: false

# TECHNICAL NOTES:
# ================
# h264_intra settings:
#   - All-Intra encoding (-g 1) ensures every frame is seekable
#   - CRF 18 provides visually lossless quality
#   - PCM audio avoids compression artifacts
#   - BT.709 color space prevents color shifts
#
# prores_hq settings:
#   - ProRes 422 HQ profile for high-quality intermediate files
#   - Uncompressed PCM audio
#   - Industry standard for professional editing
#
# fast_copy settings:
#   - Stream copy for maximum speed
#   - May have seeking issues on P-frames and B-frames
#   - Best for quick previews or when frame accuracy isn't critical

# TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
# Examples:
#   "00:00:17" = 17 seconds
#   "00:04:44" = 4 minutes 44 seconds
#   "00:00:17.500" = 17.5 seconds (500ms)
#   "00:01:30.250" = 1 minute 30.25 seconds (250ms)

segments:
  - start: "00:04:50:06"
    end: "00:05:16:17"
    title: "01"

  - start: "00:05:16:17"
    end: "00:05:31:16"
    title: "02"

  - start: "00:05:33:09"
    end: "00:05:48:18"
    title: "03"

  - start: "00:06:20:02"
    end: "00:07:19:18"
    title: "04_inahole"

  - start: "00:07:28:01"
    end: "00:08:29:08"
    title: "05"

  - start: "00:10:24:20"
    end: "00:11:28:04"
    title: "06"

  - start: "00:16:42:14"
    end: "00:17:25:22"
    title: "07_inahole"

  - start: "00:17:32:23"
    # end: "00:18:58:21"
    end: "00:19:12:04"
    title: "08"
