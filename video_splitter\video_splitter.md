# Dir `video_splitter`

### File Structure

```
├── README.md
├── project-highlights.md
├── pyproject.toml
├── run.bat
├── techstack.md
├── src
│   └── main.py
├── tests
│   └── test_precision.py
└── timecodes
    ├── 2025.01.02-kl.12.52.18--Perspektiv_a_compressed.yaml
    ├── 2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.yaml
    ├── 2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.yaml.001
    ├── 2025.08.17-kl.09.41.00--Achalasia.yaml
    ├── 2025.08.22-kl.22.56--southpark_s27e03.yaml
    ├── editing-optimized-example.yaml
    └── input-timecodes.yaml
```

---

#### `README.md`

```markdown
    # Video Splitter
    
    ## Overview
    Video segmentation tool with FFmpeg integration and rich CLI interface, supporting precise time-based video splitting with YAML/CSV configuration.
    
    ## Features
    - **Strict timecode format**: HH:MM:SS or HH:MM:SS.ms for unambiguous interpretation
    - **YAML/CSV configuration**: Load segments and settings from configuration files
    - **Interactive CLI**: User-friendly prompts with Rich-based interface
    - **Concurrent processing**: Multi-threaded segment extraction for faster processing
    - **Professional encoding profiles**: Optimized settings for video editing workflows
    - **Frame-accurate cutting**: All-Intra H.264 and ProRes support for precise editing
    - **Comprehensive validation**: Overlap detection and duration checking
    - **Progress tracking**: Real-time processing progress with visual indicators
    - **Flexible output naming**: Timestamp appending and source prefix options
    
    ## Quick Start
    Run `run.bat` to start the interactive video splitter (handles environment setup automatically)
    
    ## Usage
    ```bash
    # Interactive mode (recommended)
    run.bat
    
    # Direct command line usage
    uv run python src/main.py --prompt
    uv run python src/main.py -i "video.mp4" -op "output/" -tc "timecodes.yaml"
    ```
    
    ## Configuration File Example
    ```yaml
    # timecodes.yaml
    input_video: "path/to/video.mp4"
    output_dir: "output/segments"
    use_subdirectory: true
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    output_format: "mp4"
    
    # Encoding profile for optimal video editing workflows
    # Options: fast_copy, h264_intra, prores_hq, legacy_ae
    codec_profile: "h264_intra"
    
    # Quality control for file size (0-51)
    # - 0: Lossless (largest files)
    # - 18: Visually lossless (default)
    # - 23: Standard quality
    # - 28: Smaller files
    # - 35: Much smaller files
    quality: 18
    
    threads: 4
    append_timestamps: true
    include_source_prefix: false
    
    # Timecode format: HH:MM:SS or HH:MM:SS.ms
    segments:
      - start: "00:00:46"
        end: "00:01:16"
        title: "Introduction"
      - start: "00:01:17"
        end: "00:05:17"
        title: "Main Content"
      - start: "00:08:21"
        end: "00:11:12"
        title: "Conclusion"
    ```
    
    ## Timecode Format
    The tool uses a strict HH:MM:SS or HH:MM:SS.ms format for unambiguous interpretation:
    
    - `"00:00:17"` = 17 seconds
    - `"00:04:44"` = 4 minutes 44 seconds
    - `"00:00:17.500"` = 17.5 seconds (500ms)
    - `"00:01:30.250"` = 1 minute 30.25 seconds (250ms)
    - `"01:30:45"` = 1 hour 30 minutes 45 seconds
    
    ## Requirements
    - **FFmpeg**: Required for video processing (must be in PATH or specify path)
    - **FFprobe**: Required for video metadata extraction (usually comes with FFmpeg)
    
    ## Dependencies
    Managed via `pyproject.toml` with uv package manager.
    
    ## Encoding Profiles
    
    ### `fast_copy` (Default)
    - **Use case**: Quick extraction when frame accuracy isn't critical
    - **Method**: Stream copy (no re-encoding)
    - **Pros**: Fastest processing, smallest files
    - **Cons**: May have seeking issues in editors, cuts only on keyframes
    
    ### `h264_intra` (Recommended for Editing)
    - **Use case**: Frame-accurate editing in Premiere Pro, DaVinci Resolve, etc.
    - **Method**: All-Intra H.264 with visually lossless quality (CRF 18)
    - **Pros**: Frame-accurate cuts, smooth scrubbing, excellent quality
    - **Cons**: Larger file sizes than fast_copy
    
    ### `prores_hq` (Professional)
    - **Use case**: Professional editing workflows, color grading, high-end post-production
    - **Method**: ProRes 422 HQ with 10-bit 4:2:2 color sampling and 24-bit audio
    - **Technical**: `yuv422p10le`, `pcm_s24le`, CFR sync, optimized GOP structure
    - **Pros**: Industry standard, maximum quality, best editing performance
    - **Cons**: Very large files, requires .mov container
    
    ### `legacy_ae` (Compatibility)
    - **Use case**: Older After Effects versions or compatibility issues
    - **Method**: Standard H.264 with AAC audio
    - **Pros**: Wide compatibility
    - **Cons**: Not optimized for editing performance
    
    ## Command Line Options
    - `-i, --input_video`: Path to input video file
    - `-op, --output_path`: Output directory path
    - `-tc, --timecodes_file`: Path to YAML/CSV timecodes file
    - `--ffmpeg_path`: Path to ffmpeg executable (default: "ffmpeg")
    - `--ffprobe_path`: Path to ffprobe executable (default: "ffprobe")
    - `--output_format`: Output video format (default: "mp4")
    - `--codec_profile`: Encoding profile (fast_copy, h264_intra, prores_hq, legacy_ae)
    - `--quality`: Quality setting 0-51 (default: 18, lower = smaller files)
    - `--threads`: Number of concurrent threads (default: 1)
    - `--append_timestamps`: Append timestamps to output filenames
    - `--include_source_prefix`: Include source filename as prefix
    - `--use_subdirectory`: Create subdirectory for output files
    - `--prompt`: Interactive mode
```

---

#### `project-highlights.md`

```markdown
    # Project Highlights
    
    ## High-Value Abstractions
    
    ### 1. Project Name Implications
    **VideoSplitter** â†’ Precision video segmentation tool with frame-accurate temporal control
    
    ### 2. Directory Structure Insights
    - **Modern Python packaging**: `pyproject.toml` + `uv` dependency management
    - **Timecode-driven workflow**: Dedicated `timecodes/` with YAML configurations
    - **CLI-first architecture**: Single `src/main.py` entry point with Rich-based UX
    - **Test-driven development**: Structured `tests/` directory
    - **Professional encoding**: Multiple FFmpeg profiles for editing workflows
    
    ### 3. Core Value Proposition
    Frame-accurate video segmentation with professional encoding profiles, optimized for video editing workflows with concurrent processing capabilities.
    
    ### 4. Comment-to-Code Ratio Assessment
    - **Total lines**: 816
    - **Comment lines**: 18
    - **Ratio**: ~2.2% (minimal, well-structured code)
    - **Style**: Concise inline comments for complex logic sections only
    
    ## Technical Architecture
    
    ### Core Components
    - **TimecodeParser**: Strict-but-friendly parsing (HH:MM:SS.mmm, HH:MM:SS:FF)
    - **VideoSegment**: Type-safe dataclass representation
    - **Concurrent processing**: ThreadPoolExecutor for parallel segment extraction
    - **Interactive CLI**: Rich-based prompts with configuration discovery
    
    ### Key Features
    - **Multiple timecode formats**: Milliseconds and frame-based precision
    - **Professional encoding profiles**: h264_intra, ProRes HQ, legacy AE compatibility
    - **Configuration-driven**: YAML-based segment definitions with CLI overrides
    - **Auto-detection**: FPS and format inference from video metadata
    - **Error handling**: Comprehensive validation with user-friendly messaging
    
    ## Current State Analysis
    - **Mature CLI interface**: Fully interactive with intelligent defaults
    - **Production-ready**: Comprehensive error handling and validation
    - **Extensible architecture**: Clean separation of concerns
    - **Windows-optimized**: PowerShell integration with batch runner
```

---

#### `pyproject.toml`

```toml
    [project]
    name = "video-splitter"
    version = "1.0.0"
    description = "Video segmentation tool with FFmpeg integration and rich CLI interface"
    requires-python = ">=3.9"
    
    dependencies = [
        "loguru",
        "PyYAML",
        "rich",
    ]
```

---

#### `run.bat`

```batch
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    :: =============================================================================
    :: Video Splitter - Universal Runner
    :: =============================================================================
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    
    :: Check for uv installation
    WHERE uv >nul 2>&1
    IF ERRORLEVEL 1 (
        ECHO [ERROR] uv is not installed or not in PATH
        ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
        ECHO.
        ECHO Falling back to traditional Python execution...
        python src\main.py --prompt %*
        GOTO :End
    )
    
    :: Check for pyproject.toml
    IF NOT EXIST "pyproject.toml" (
        ECHO [ERROR] pyproject.toml not found
        ECHO Please ensure the project is properly configured
        PAUSE>NUL & EXIT /B
    )
    
    :: Initialize environment if needed
    IF NOT EXIST ".venv" (
        ECHO [INFO] Initializing uv environment...
        uv sync
        IF ERRORLEVEL 1 (
            ECHO [ERROR] Failed to initialize environment
            PAUSE>NUL & EXIT /B
        )
        ECHO [SUCCESS] Environment initialized
        ECHO.
    )
    
    :: Run the application
    ECHO [INFO] Starting Video Splitter...
    uv run python src\main.py --prompt %*
    
    :End
    ECHO.
    ECHO Press any key to exit...
    PAUSE >NUL
    EXIT /B
```

---

#### `techstack.md`

```markdown
    # Technology Stack
    
    ## Core Technologies
    - **Python 3.9+**: Primary language
    - **uv**: Package manager and environment management
    - **FFmpeg/FFprobe**: Video processing engine
    
    ## Dependencies
    - **loguru**: Structured logging
    - **PyYAML**: Configuration file parsing
    - **rich**: CLI interface and progress visualization
    
    ## Architecture
    - **Concurrent processing**: ThreadPoolExecutor for parallel segment extraction
    - **Dataclass patterns**: Type-safe segment representation
    - **CLI-first design**: Interactive prompts with Rich-based UX
    - **Configuration-driven**: YAML-based segment definitions
    - **Professional encoding profiles**: Optimized FFmpeg settings for video editing workflows
    - **Frame-accurate cutting**: All-Intra and ProRes support for precise temporal control
    
    ## Development Environment
    - **Windows-optimized**: PowerShell batch runner
    - **Virtual environment**: uv-managed isolation
    - **Project structure**: Standard Python package layout
```

---

#### `src\main.py`

```python
    import argparse
    import os
    import subprocess
    import sys
    from concurrent.futures import ThreadPoolExecutor, as_completed
    from dataclasses import dataclass
    from decimal import Decimal, ROUND_DOWN
    from pathlib import Path
    from typing import Dict, List, Optional, Tuple, Any
    import re
    
    import yaml
    from loguru import logger
    from rich.console import Console
    from rich.panel import Panel
    from rich.progress import (Progress, SpinnerColumn, TextColumn,
                              BarColumn, TimeElapsedColumn)
    from rich.prompt import Prompt, Confirm
    from rich.table import Table
    
    console = Console()
    
    
    class TimecodeParser:
        """Strict-but-friendly parser:
           - HH:MM:SS[.mmm]  => milliseconds
           - HH:MM:SS:FF     => frames (requires fps)
           - Optional: treat HH:MM:SS.FF as frames when decimal_as_frames=True
        """
    
        @staticmethod
        def parse_timecode(
            time_str: str,
            fps: Optional[int] = None,
            decimal_as_frames: bool = False
        ) -> float:
            """Return total seconds with ms precision.
    
            Accepted forms:
            - HH:MM:SS
            - HH:MM:SS.m[mm]        (milliseconds unless decimal_as_frames=True and looks like frames)
            - HH:MM:SS:FF           (frames; requires fps; FF < fps)
            """
            time_str = time_str.strip()
    
            # 1) Frames with colon: HH:MM:SS:FF
            m_frames = re.match(r'^(\d{1,2}):(\d{2}):(\d{2}):(\d{2})$', time_str)
            if m_frames:
                if fps is None:
                    raise ValueError("Timecode uses frames (HH:MM:SS:FF) but fps was not provided.")
                hours = int(m_frames.group(1))
                minutes = int(m_frames.group(2))
                seconds = int(m_frames.group(3))
                frames = int(m_frames.group(4))
    
                if minutes >= 60:
                    raise ValueError(f"Invalid minutes: {minutes} (must be < 60)")
                if seconds >= 60:
                    raise ValueError(f"Invalid seconds: {seconds} (must be < 60)")
                if frames >= fps:
                    raise ValueError(f"Invalid frames: {frames} (must be < fps={fps})")
    
                return hours * 3600 + minutes * 60 + seconds + frames / float(fps)
    
            # 2) Milliseconds or ".FF as frames": HH:MM:SS[.mmm]
            m_ms = re.match(r'^(\d{1,2}):(\d{2}):(\d{2})(?:\.(\d{1,3}))?$', time_str)
            if m_ms:
                hours = int(m_ms.group(1))
                minutes = int(m_ms.group(2))
                seconds = int(m_ms.group(3))
                frac = m_ms.group(4)
    
                if minutes >= 60:
                    raise ValueError(f"Invalid minutes: {minutes} (must be < 60)")
                if seconds >= 60:
                    raise ValueError(f"Invalid seconds: {seconds} (must be < 60)")
    
                if frac is None:
                    return hours * 3600 + minutes * 60 + seconds
    
                # If requested, treat short decimals as frames when plausible
                if decimal_as_frames and fps:
                    if 1 <= len(frac) <= 2:
                        ff = int(frac)
                        if ff < fps:
                            return hours * 3600 + minutes * 60 + seconds + ff / float(fps)
    
                # Otherwise: normalize milliseconds (.5 -> .500, .05 -> .050)
                milliseconds = int(frac.ljust(3, '0'))
                if milliseconds >= 1000:
                    raise ValueError(f"Invalid milliseconds: {milliseconds} (must be < 1000)")
                return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
    
            raise ValueError(f"Invalid timecode: '{time_str}'. Use HH:MM:SS[.mmm] or HH:MM:SS:FF.")
    
        @staticmethod
        def format_seconds(seconds: float) -> str:
            """Format seconds as HH:MM:SS.mmm with millisecond precision, no rounding up."""
            d = Decimal(str(seconds)).quantize(Decimal('0.001'), rounding=ROUND_DOWN)
            total_ms = int(d * 1000)
    
            hours, rem_ms = divmod(total_ms, 3600_000)
            minutes, rem_ms = divmod(rem_ms, 60_000)
            secs, milliseconds = divmod(rem_ms, 1000)
    
            if milliseconds > 0:
                return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"
            else:
                return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
        @staticmethod
        def format_duration(seconds: float) -> str:
            return TimecodeParser.format_seconds(seconds)
    
    
    @dataclass
    class VideoSegment:
        name: str
        start: str
        end: str
    
    
    def parse_arguments():
        parser = argparse.ArgumentParser(
            description="Split a video into segments using FFmpeg"
        )
        parser.add_argument("-i", "--input_video", type=str, help="Path to input video file")
        parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
        parser.add_argument("-tc", "--timecodes_file", type=str, help="Path to YAML/CSV timecodes file")
        parser.add_argument("--ffmpeg_path", default="ffmpeg", help="Path to ffmpeg executable")
        parser.add_argument("--ffprobe_path", default="ffprobe", help="Path to ffprobe executable")
        parser.add_argument("--output_format", default="mp4", help="Output video format")
        parser.add_argument("--codec_profile", default="h264_intra",
                            choices=["fast_copy", "h264_intra", "prores_hq", "legacy_ae"],
                            help="Encoding profile")
        parser.add_argument("--quality", type=int, default=18, choices=range(0, 52),
                            help="CRF quality (0-51)")
        parser.add_argument("--threads", type=int, default=1, help="Number of concurrent threads")
        parser.add_argument("--append_timestamps", action="store_true", default=True,
                            help="Append timestamps to output filenames")
        parser.add_argument("--include_source_prefix", action="store_true", default=False,
                            help="Include source filename as prefix")
        parser.add_argument("--use_subdirectory", action="store_true", default=False,
                            help="Create subdirectory for output files")
        parser.add_argument("--prompt", action="store_true", help="Interactive mode")
        parser.add_argument("--fps", type=int, default=None,
                            help="Frames per second. Used for HH:MM:SS:FF and optionally HH:MM:SS.FF.")
        parser.add_argument("--decimal-frames", choices=["auto", "on", "off"], default="auto",
                            help="Interpret HH:MM:SS.FF as frames (FF) instead of fractional seconds. Default: auto.")
        return parser.parse_args()
    
    
    def setup_logger():
        logger.remove()
        logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
    
    
    def wait_for_user_exit():
        console.print("\n[dim]Press Enter to exit...[/dim]")
        input()
    
    
    def get_video_duration(video_path: str, ffprobe_path: str = "ffprobe") -> float:
        cmd = [
            ffprobe_path, "-v", "error", "-show_entries", "format=duration",
            "-of", "default=noprint_wrappers=1:nokey=1", video_path
        ]
        try:
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            return float(result.stdout.decode().strip())
        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"Failed to get video duration: {e.stderr.decode().strip()}")
    
    
    def get_video_fps(video_path: str, ffprobe_path: str = "ffprobe") -> Optional[int]:
        """Detect fps and snap 23.976/29.97/59.94 to 24/30/60."""
        cmd = [
            ffprobe_path, "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "stream=avg_frame_rate",
            "-of", "default=noprint_wrappers=1:nokey=1",
            video_path
        ]
        try:
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            val = result.stdout.decode().strip()
            if "/" in val:
                num, den = val.split("/", 1)
                den = float(den)
                if den == 0:
                    return None
                fps_float = float(num) / den
            else:
                fps_float = float(val)
            common = [24, 25, 30, 50, 60]
            nearest = min(common, key=lambda c: abs(c - fps_float))
            if abs(nearest - fps_float) < 0.6:
                return nearest
            return int(round(fps_float))
        except Exception:
            return None
    
    
    def load_timecodes_from_yaml(file_path: str) -> Dict[str, Any]:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f) or {}
    
        segments = []
        for seg in data.get('segments', []):
            if seg.get('start') and seg.get('end') and (seg.get('title') or seg.get('name')):
                segments.append((
                    seg['start'].strip(),
                    seg['end'].strip(),
                    (seg.get('title') or seg.get('name')).strip()
                ))
    
        # Allow decimal_frames in YAML as true/false/auto
        yaml_decimal_frames = data.get('decimal_frames', 'auto')
        if isinstance(yaml_decimal_frames, bool):
            yaml_decimal_frames = 'on' if yaml_decimal_frames else 'off'
        else:
            yaml_decimal_frames = str(yaml_decimal_frames).strip().lower()
            if yaml_decimal_frames not in ('auto', 'on', 'off', 'true', 'false', 'yes', 'no'):
                yaml_decimal_frames = 'auto'
            if yaml_decimal_frames in ('true', 'yes'):
                yaml_decimal_frames = 'on'
            if yaml_decimal_frames in ('false', 'no'):
                yaml_decimal_frames = 'off'
    
        return {
            'input_video': data.get('input_video'),
            'output_dir': data.get('output_dir'),
            'segments': segments,
            'ffmpeg_path': data.get('ffmpeg_path', 'ffmpeg'),
            'ffprobe_path': data.get('ffprobe_path', 'ffprobe'),
            'output_format': data.get('output_format', 'mp4'),
            'codec_profile': data.get('codec_profile', 'h264_intra'),
            'quality': data.get('quality', 18),
            'threads': data.get('threads', 1),
            'append_timestamps': data.get('append_timestamps', True),
            'include_source_prefix': data.get('include_source_prefix', False),
            'use_subdirectory': data.get('use_subdirectory', False),
            'fps': data.get('fps'),
            'decimal_frames': yaml_decimal_frames
        }
    
    
    def _has_colon_frames(segs: List[Tuple[str, str, str]]) -> bool:
        pat = re.compile(r'^\d{1,2}:\d{2}:\d{2}:\d{2}$')
        for s, e, _ in segs:
            if pat.match(s) or pat.match(e):
                return True
        return False
    
    
    def _decimals_look_like_frames(segs: List[Tuple[str, str, str]], fps: Optional[int]) -> bool:
        """Heuristic: if all decimal fractions are 1–2 digits, all < fps,
        and there is no 3-digit .mmm anywhere → assume they're frames."""
        if not fps:
            return False
        any_fraction = False
        has_three = False
        values: List[int] = []
        pat = re.compile(r'^\d{1,2}:\d{2}:\d{2}\.(\d{1,3})$')
        for s, e, _ in segs:
            for t in (s, e):
                m = pat.match(t)
                if not m:
                    continue
                any_fraction = True
                frac = m.group(1)
                if len(frac) == 3:
                    has_three = True
                elif len(frac) in (1, 2):
                    try:
                        values.append(int(frac))
                    except Exception:
                        return False
        if has_three:
            return False
        if not any_fraction:
            return False
        if not values:
            return False
        return all(0 <= v < fps for v in values)
    
    
    def _resolve_decimal_frames_mode(cli_mode: str, yaml_mode: str) -> str:
        """CLI overrides YAML. Normalize to 'auto'|'on'|'off'."""
        pref = cli_mode or 'auto'
        if pref not in ('auto', 'on', 'off'):
            pref = 'auto'
        y = yaml_mode or 'auto'
        if y not in ('auto', 'on', 'off'):
            y = 'auto'
        return pref if pref != 'auto' else y
    
    
    def _normalize_segments(
        segs: List[Tuple[str, str, str]],
        fps: Optional[int],
        decimal_as_frames: bool
    ) -> List[Tuple[str, str, str]]:
        """Parse and reformat all segments to canonical HH:MM:SS.mmm strings."""
        normalized: List[Tuple[str, str, str]] = []
        for s, e, name in segs:
            s_sec = TimecodeParser.parse_timecode(s, fps=fps, decimal_as_frames=decimal_as_frames)
            e_sec = TimecodeParser.parse_timecode(e, fps=fps, decimal_as_frames=decimal_as_frames)
            if e_sec <= s_sec:
                raise ValueError(f"Invalid segment '{name}': end <= start ({s} -> {e})")
            s_fmt = TimecodeParser.format_seconds(s_sec)
            e_fmt = TimecodeParser.format_seconds(e_sec)
            normalized.append((s_fmt, e_fmt, name))
        return normalized
    
    
    def prompt_for_segments(video_duration: float, fps: Optional[int], decimal_as_frames: bool) -> List[Tuple[str, str, str]]:
        formatted_dur = TimecodeParser.format_duration(video_duration)
        fmts = "HH:MM:SS.mmm (milliseconds) or HH:MM:SS:FF (frames)"
        console.print(f"\n[bold cyan]Video Duration:[/bold cyan] [dim]{formatted_dur}[/dim] | "
                      f"[bold cyan]Format:[/bold cyan] [dim]{fmts}[/dim]\n")
        if fps:
            mode = "frames" if decimal_as_frames else "milliseconds"
            console.print(f"[bold cyan]FPS:[/bold cyan] [dim]{fps}[/dim]  "
                          f"[bold cyan]'.xx' decimals interpreted as:[/bold cyan] [dim]{mode}[/dim]\n")
    
        result: List[Tuple[str, str, str]] = []
        idx = 1
    
        while True:
            name = Prompt.ask(f"[bold cyan]Segment {idx} name[/bold cyan]", default=f"clip{idx}")
    
            start_sec = None
            while start_sec is None:
                st = Prompt.ask("[bold cyan]Start time (HH:MM:SS.mmm or HH:MM:SS:FF)[/bold cyan]")
                try:
                    start_sec = TimecodeParser.parse_timecode(st, fps=fps, decimal_as_frames=decimal_as_frames)
                except ValueError as v:
                    console.print(f"[bold red]{v}[/bold red]")
    
            end_sec = None
            while end_sec is None:
                en = Prompt.ask("[bold cyan]End time (HH:MM:SS.mmm or HH:MM:SS:FF)[/bold cyan]")
                try:
                    en_sec = TimecodeParser.parse_timecode(en, fps=fps, decimal_as_frames=decimal_as_frames)
                    if en_sec <= start_sec:
                        raise ValueError("End must be greater than start.")
                    if en_sec > video_duration:
                        raise ValueError("End exceeds video duration.")
                    end_sec = en_sec
                except ValueError as v:
                    console.print(f"[bold red]{v}[/bold red]")
    
            st_form = TimecodeParser.format_seconds(start_sec)
            en_form = TimecodeParser.format_seconds(end_sec)
            result.append((st_form, en_form, name))
            console.print(f"[bold green]Added '{name}': {st_form} to {en_form}[/bold green]\n")
            idx += 1
    
            if not Confirm.ask("[bold cyan]Add another segment?[/bold cyan]", default=False):
                break
    
        return result
    
    
    def discover_and_select_timecode_file() -> Optional[str]:
        timecodes_dir = Path("timecodes")
        if not timecodes_dir.exists():
            console.print(f"[bold yellow]Timecodes directory '{timecodes_dir}' not found[/bold yellow]")
            return None
        yaml_files = list(timecodes_dir.glob("*.yaml")) + list(timecodes_dir.glob("*.yml"))
        if not yaml_files:
            console.print(f"[bold yellow]No YAML timecode files found in '{timecodes_dir}'[/bold yellow]")
            return None
    
        console.print(f"\n[bold cyan]Available timecode files in '{timecodes_dir}':[/bold cyan]")
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("No.", style="cyan", width=4)
        table.add_column("Filename", style="green")
        table.add_column("Size", style="dim", width=8)
    
        for idx, file_path in enumerate(yaml_files, 1):
            file_size = f"{file_path.stat().st_size}B" if file_path.exists() else "N/A"
            table.add_row(str(idx), file_path.name, file_size)
    
        console.print(table)
    
        while True:
            try:
                choice = Prompt.ask(
                    f"[bold cyan]Select timecode file (1-{len(yaml_files)}) or 'q' to skip[/bold cyan]",
                    default="q"
                )
                if choice.lower() == 'q':
                    return None
                file_idx = int(choice) - 1
                if 0 <= file_idx < len(yaml_files):
                    selected_file = str(yaml_files[file_idx])
                    console.print(f"[bold green]Selected: {selected_file}[/bold green]")
                    return selected_file
                else:
                    console.print(f"[bold red]Invalid selection. Choose 1-{len(yaml_files)} or 'q'[/bold red]")
            except ValueError:
                console.print(f"[bold red]Invalid input. Enter a number (1-{len(yaml_files)}) or 'q'[/bold red]")
    
    
    def check_output_directory(output_dir: str):
        if not Path(output_dir).exists():
            if Confirm.ask(f"[bold cyan]Create output directory '{output_dir}'?[/bold cyan]", default=True):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    console.print(f"[bold green]Created directory: {output_dir}[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Error creating directory: {e}[/bold red]")
                    sys.exit(1)
            else:
                console.print("[yellow]Operation cancelled by user[/yellow]")
                sys.exit(1)
    
    
    def get_user_inputs(args):
        yaml_config: Dict[str, Any] = {}
    
        # Load YAML if desired
        if args.prompt and not args.timecodes_file:
            if Confirm.ask("[bold cyan]Load a YAML/CSV timecodes file?[/bold cyan]", default=True):
                while True:
                    timecodes_path = Prompt.ask("[bold cyan]Path to timecodes file (or press Enter to browse)[/bold cyan]", default="")
                    if not timecodes_path.strip():
                        selected_file = discover_and_select_timecode_file()
                        if selected_file:
                            args.timecodes_file = selected_file
                        break
                    if Path(timecodes_path).is_file():
                        args.timecodes_file = timecodes_path
                        break
                    else:
                        console.print(f"[bold red]File not found: {timecodes_path}[/bold red]")
    
        if args.timecodes_file and Path(args.timecodes_file).is_file():
            try:
                yaml_config = load_timecodes_from_yaml(args.timecodes_file)
                console.print(f"[bold green]Loaded configuration from {args.timecodes_file}[/bold green]")
            except Exception as e:
                console.print(f"[bold red]Error loading YAML file: {e}[/bold red]")
    
        # Input video
        if args.prompt or not args.input_video:
            default_video = yaml_config.get('input_video') or args.input_video or ""
            args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]", default=default_video)
            while not Path(args.input_video).is_file():
                console.print("[bold red]File not found. Try again.[/bold red]")
                args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]")
        if not args.input_video or not Path(args.input_video).is_file():
            console.print("[bold red]Error: Input video file not found[/bold red]")
            sys.exit(1)
    
        # Early FPS auto-detect if not provided
        if getattr(args, "fps", None) in (None, 0):
            detected = get_video_fps(args.input_video, yaml_config.get('ffprobe_path', args.ffprobe_path))
            if detected:
                if args.prompt:
                    if Confirm.ask(f"[bold cyan]Detected FPS {detected}. Use it?[/bold cyan]", default=True):
                        args.fps = detected
                else:
                    args.fps = detected
    
        # Interactive options
        if args.prompt:
            use_subdir = Confirm.ask("[bold cyan]Place output in a separate subdirectory?[/bold cyan]",
                                     default=yaml_config.get("use_subdirectory", False))
            if use_subdir:
                args.output_path = Prompt.ask("[bold cyan]Output directory[/bold cyan]",
                                              default=yaml_config.get("output_dir", "output_segments"))
            else:
                args.output_path = str(Path(args.input_video).parent)
    
            args.append_timestamps = Confirm.ask("[bold cyan]Append timestamps to filenames?[/bold cyan]",
                                                 default=yaml_config.get("append_timestamps", True))
            args.include_source_prefix = Confirm.ask("[bold cyan]Include source file's name as prefix?[/bold cyan]",
                                                     default=yaml_config.get("include_source_prefix", False))
    
            console.print("\n[bold cyan]Encoding Profile Options:[/bold cyan]")
            console.print("  [dim]fast_copy[/dim]    - Stream copy (fastest, may have seeking issues)")
            console.print("  [dim]h264_intra[/dim]   - All-Intra H.264 (frame-accurate, editing-optimized)")
            console.print("  [dim]prores_hq[/dim]    - ProRes 422 HQ (professional mezzanine)")
            console.print("  [dim]legacy_ae[/dim]    - Legacy After Effects compatibility")
    
            default_profile = yaml_config.get("codec_profile", "h264_intra")
            args.codec_profile = Prompt.ask("[bold cyan]Select encoding profile[/bold cyan]",
                                            choices=["fast_copy", "h264_intra", "prores_hq", "legacy_ae"],
                                            default=default_profile)
    
            if args.codec_profile != "fast_copy":
                console.print("\n[bold cyan]Quality Settings (CRF):[/bold cyan]")
                console.print("  [dim]0[/dim]     - Lossless (largest files)")
                console.print("  [dim]18[/dim]    - Visually lossless (default)")
                console.print("  [dim]23[/dim]    - Standard quality")
                console.print("  [dim]28[/dim]    - Smaller files, good quality")
                console.print("  [dim]35[/dim]    - Much smaller files, noticeable quality loss")
    
                default_quality = yaml_config.get("quality", 18)
                args.quality = Prompt.ask("[bold cyan]Select quality setting (0-51)[/bold cyan]",
                                          default=str(default_quality))
                try:
                    args.quality = int(args.quality)
                    if not 0 <= args.quality <= 51:
                        raise ValueError("Quality must be between 0 and 51")
                except ValueError:
                    console.print("[bold red]Invalid quality setting. Using default (18)[/bold red]")
                    args.quality = 18
        elif not args.output_path:
            default_output = yaml_config.get('output_dir') or args.output_path
            if not default_output:
                default_output = "output_segments" if yaml_config.get('use_subdirectory', False) else str(Path(args.input_video).parent)
            args.output_path = default_output
    
        # Segments (load or prompt)
        segments = yaml_config.get('segments', [])
        if args.prompt or not segments:
            if segments:
                console.print(f"[bold green]Found {len(segments)} segments from YAML/CSV.[/bold green]")
                if args.prompt:
                    use_existing = Confirm.ask("[bold cyan]Use these segments as-is?[/bold cyan]", default=True)
                    if not use_existing:
                        discard = Confirm.ask("[bold cyan]Discard loaded segments and define new ones?[/bold cyan]", default=True)
                        if discard:
                            segments = []
            if not segments:
                try:
                    duration = get_video_duration(args.input_video, yaml_config.get('ffprobe_path', args.ffprobe_path))
                    fps_for_prompt = yaml_config.get('fps', getattr(args, 'fps', None))
                    # Decide decimal-as-frames mode for prompt (use YAML/CLI mode if present)
                    mode = _resolve_decimal_frames_mode(getattr(args, "decimal_frames", "auto"),
                                                        yaml_config.get("decimal_frames", "auto"))
                    auto_frames = _decimals_look_like_frames([], fps_for_prompt)  # none yet, so False
                    decimal_as_frames_prompt = (mode == 'on') or (mode == 'auto' and auto_frames)
                    segments = prompt_for_segments(duration, fps=fps_for_prompt, decimal_as_frames=decimal_as_frames_prompt)
                except Exception as e:
                    console.print(f"[bold red]Error getting video duration: {e}[/bold red]")
                    sys.exit(1)
    
        # Merge YAML config with CLI (CLI takes precedence)
        for key, value in yaml_config.items():
            if not hasattr(args, key) or getattr(args, key) is None:
                setattr(args, key, value)
    
        # Resolve decimal-frames mode (CLI overrides YAML)
        args.decimal_frames = _resolve_decimal_frames_mode(getattr(args, "decimal_frames", "auto"),
                                                           yaml_config.get("decimal_frames", "auto"))
    
        # Final decision: should decimals be treated as frames?
        uses_colon_frames = _has_colon_frames(segments)
        auto_guess = _decimals_look_like_frames(segments, getattr(args, "fps", None)) or uses_colon_frames
        decimal_as_frames_effective = (args.decimal_frames == 'on') or (args.decimal_frames == 'auto' and auto_guess)
    
        # If any HH:MM:SS:FF present and fps missing, try to detect or ask
        if uses_colon_frames and getattr(args, "fps", None) in (None, 0):
            detected_fps = get_video_fps(args.input_video, getattr(args, "ffprobe_path", "ffprobe"))
            if args.prompt:
                if detected_fps:
                    if Confirm.ask(f"[bold cyan]Segments include frame timecodes. Use detected FPS {detected_fps}?[/bold cyan]", default=True):
                        args.fps = detected_fps
                else:
                    while True:
                        val = Prompt.ask("[bold cyan]Enter FPS for HH:MM:SS:FF timecodes[/bold cyan]")
                        try:
                            args.fps = int(val)
                            if args.fps <= 0:
                                raise ValueError
                            break
                        except Exception:
                            console.print("[bold red]Invalid FPS. Enter a positive integer.[/bold red]")
            else:
                if detected_fps:
                    args.fps = detected_fps
                else:
                    raise ValueError("Segments use HH:MM:SS:FF but fps is not set and could not be detected. Provide fps in YAML (fps: 25) or CLI (--fps 25).")
    
        # Normalize segments to canonical .mmm strings using the effective rules
        try:
            segments = _normalize_segments(segments, fps=getattr(args, "fps", None), decimal_as_frames=decimal_as_frames_effective)
        except Exception as e:
            console.print(f"[bold red]Error normalizing segments:[/bold red] {e}")
            sys.exit(1)
    
        # Persist the effective flags for display
        args.decimal_frames_effective = decimal_as_frames_effective
    
        return args.input_video, args.output_path, segments, args
    
    
    def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str,
                           ffmpeg_path: str, output_format: str, codec_profile: str,
                           append_timestamps: bool, include_source_prefix: bool, quality: int = 18) -> Tuple[str, Optional[str], Optional[str]]:
        source_name = Path(input_video).stem if include_source_prefix else ""
        prefix = f"{source_name}_" if include_source_prefix else ""
    
        if codec_profile == "prores_hq":
            output_format = "mov"
    
        if append_timestamps:
            start_clean = segment.start.replace(':', '-').replace('.', '_')
            end_clean = segment.end.replace(':', '-').replace('.', '_')
            filename = f"{prefix}{segment.name}_{start_clean}_to_{end_clean}.{output_format}"
        else:
            filename = f"{prefix}{segment.name}.{output_format}"
    
        output_path = Path(output_dir) / filename
    
        # Use normalized, canonical strings for exact ffmpeg interpretation
        cmd = [ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end]
    
        if codec_profile == "fast_copy":
            cmd.extend(["-c", "copy", "-avoid_negative_ts", "make_zero", "-y", str(output_path)])
        elif codec_profile == "h264_intra":
            cmd.extend([
                "-c:v", "libx264", "-preset", "medium", "-crf", str(quality), "-g", "1",
                "-bf", "0", "-pix_fmt", "yuv420p", "-color_primaries", "bt709", "-color_trc", "bt709",
                "-colorspace", "bt709", "-color_range", "tv",
                "-c:a", "pcm_s24le", "-ar", "48000",
                "-vsync", "cfr",
                "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
                "-y", str(output_path)
            ])
        elif codec_profile == "prores_hq":
            cmd.extend([
                "-c:v", "prores_ks", "-profile:v", "3",
                "-pix_fmt", "yuv422p10le",
                "-vendor", "apl0",
                "-bits_per_mb", "8000",
                "-g", "30", "-bf", "0",
                "-colorspace", "bt709", "-color_primaries", "bt709", "-color_trc", "bt709",
                "-c:a", "pcm_s24le", "-ar", "48000",
                "-vsync", "cfr",
                "-avoid_negative_ts", "make_zero",
                "-y", str(output_path)
            ])
        elif codec_profile == "legacy_ae":
            cmd.extend([
                "-c:v", "libx264", "-preset", "faster", "-pix_fmt", "yuv420p",
                "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart",
                "-y", str(output_path)
            ])
        else:
            cmd.extend([
                "-c:v", "libx264", "-preset", "medium", "-crf", str(quality), "-g", "1",
                "-bf", "0", "-pix_fmt", "yuv420p", "-colorspace", "bt709",
                "-color_primaries", "bt709", "-color_trc", "bt709", "-color_range", "tv",
                "-c:a", "pcm_s24le", "-ar", "48000",
                "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
                "-y", str(output_path)
            ])
    
        try:
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return segment.name, str(output_path), None
        except subprocess.CalledProcessError as e:
            return segment.name, None, e.stderr.decode().strip()
    
    
    def process_video_segments(input_video: str, output_dir: str,
                               segments: List[Tuple[str, str, str]],
                               config: argparse.Namespace) -> List[Tuple[str, str]]:
        Path(output_dir).mkdir(parents=True, exist_ok=True)
    
        segment_objects = [VideoSegment(name, start, end) for start, end, name in segments]
        results: List[Tuple[str, str]] = []
    
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=console,
            transient=True
        ) as progress:
            task = progress.add_task("[bold cyan]Processing video segments...[/bold cyan]",
                                     total=len(segment_objects))
    
            with ThreadPoolExecutor(max_workers=config.threads) as executor:
                future_to_segment = {
                    executor.submit(
                        split_video_segment,
                        input_video,
                        segment,
                        output_dir,
                        config.ffmpeg_path,
                        config.output_format,
                        getattr(config, 'codec_profile', 'h264_intra'),
                        config.append_timestamps,
                        config.include_source_prefix,
                        config.quality
                    ): segment.name for segment in segment_objects
                }
    
                for future in as_completed(future_to_segment):
                    segment_name = future_to_segment[future]
                    try:
                        name, output_path, error = future.result()
                        if output_path:
                            results.append((name, output_path))
                            console.print(f"[bold green]✓ Completed: {name}[/bold green]")
                        else:
                            results.append((name, f"Failed: {error}"))
                            console.print(f"[bold red]✗ Failed: {name}[/bold red]")
                    except Exception as e:
                        error_msg = f"Exception: {str(e)}"
                        results.append((segment_name, error_msg))
                        console.print(f"[bold red]✗ Exception: {segment_name}[/bold red]")
                    finally:
                        progress.advance(task)
    
        return results
    
    
    def display_summary(input_video: str, output_dir: str,
                        segments: List[Tuple[str, str, str]],
                        config: argparse.Namespace):
        table = Table(title="Video Splitting Configuration", show_header=False, box=None)
        table.add_column("Parameter", style="bold cyan")
        table.add_column("Value", style="dim")
    
        table.add_row("Input Video", input_video)
        table.add_row("Output Directory", output_dir)
        table.add_row("Output Format", config.output_format)
        table.add_row("Codec Profile", getattr(config, 'codec_profile', 'h264_intra'))
        table.add_row("Threads", str(config.threads))
        table.add_row("Append Timestamps", "Yes" if config.append_timestamps else "No")
        table.add_row("Include Source Prefix", "Yes" if config.include_source_prefix else "No")
        table.add_row("FPS (frames mode)", str(getattr(config, "fps", "—")))
        mode_label = getattr(config, "decimal_frames", "auto")
        eff = "Yes" if getattr(config, "decimal_frames_effective", False) else "No"
        table.add_row("'.xx' decimals treated as frames", f"{eff} (mode: {mode_label})")
        table.add_row("Segments", str(len(segments)))
        table.add_row("Quality Setting", str(config.quality))
    
        console.print("\n[bold blue]--- Operation Summary ---[/bold blue]")
        console.print(Panel(table, border_style="blue"))
    
        if segments:
            console.print("\n[bold cyan]Timecode Interpretation:[/bold cyan]")
            seg_table = Table(show_header=True, header_style="bold magenta")
            seg_table.add_column("Name", style="cyan")
            seg_table.add_column("Start", style="green")
            seg_table.add_column("End", style="green")
            seg_table.add_column("Interpreted As", style="yellow")
    
            for start, end, name in segments:
                try:
                    start_sec = TimecodeParser.parse_timecode(start, fps=getattr(config, "fps", None))
                    end_sec = TimecodeParser.parse_timecode(end, fps=getattr(config, "fps", None))
                    duration = end_sec - start_sec
                    interpretation = f"{start_sec:.3f}s → {end_sec:.3f}s ({duration:.3f}s)"
                    seg_table.add_row(name, start, end, interpretation)
                except Exception as e:
                    seg_table.add_row(name, start, end, f"ERROR: {e}")
            console.print(seg_table)
    
    
    def display_results(results: List[Tuple[str, str]]):
        table = Table(title="Processing Results", show_header=True, header_style="bold cyan")
        table.add_column("Segment", style="cyan")
        table.add_column("Status", style="dim")
        table.add_column("Output", style="dim")
    
        for name, result in results:
            if result.startswith("Failed") or result.startswith("Exception"):
                table.add_row(name, "[bold red]Failed[/bold red]", result)
            else:
                table.add_row(name, "[bold green]Success[/bold green]", result)
    
        console.print("\n")
        console.print(table)
    
    
    def main():
        args = parse_arguments()
        input_video, output_path, segments, config = get_user_inputs(args)
    
        setup_logger()
    
        try:
            check_output_directory(output_path)
            display_summary(input_video, output_path, segments, config)
    
            if not Confirm.ask("\n[bold cyan]Proceed with video splitting?[/bold cyan]", default=True):
                console.print("[yellow]Operation cancelled by user[/yellow]")
                return
    
            console.print("\n[bold cyan]Starting video processing...[/bold cyan]")
            results = process_video_segments(input_video, output_path, segments, config)
    
            display_results(results)
    
            def is_failed(result):
                return (result.startswith("Failed") or result.startswith("Exception"))
    
            successful = all(not is_failed(result) for _, result in results)
    
            if successful:
                console.print("\n[bold green]All segments processed successfully![/bold green]")
            else:
                console.print("\n[bold yellow]Some segments failed to process. Check the results above.[/bold yellow]")
    
        except Exception as e:
            console.print(f"\n[bold red]Error:[/bold red] {e}")
            logger.exception(f"Main execution error: {e}")
        finally:
            console.print("\n[bold green]Video splitting completed.[/bold green]")
            wait_for_user_exit()
    
    
    if __name__ == "__main__":
        main()
```

---

#### `tests\test_precision.py`

```python
    #!/usr/bin/env python3
    """
    Test script to verify millisecond precision fixes in TimecodeParser.
    """
    
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'video_splitter', 'src'))
    
    from main import TimecodeParser
    
    def test_parse_precision():
        """Test that parsing handles fractional seconds correctly."""
        print("Testing TimecodeParser.parse_timecode precision...")
    
        test_cases = [
            ("00:00:17", 17.000),
            ("00:00:17.5", 17.500),      # Should be 500ms, not 5ms
            ("00:00:17.05", 17.050),     # Should be 50ms, not 5ms
            ("00:00:17.500", 17.500),
            ("00:01:30.250", 90.250),
            ("01:00:00.001", 3600.001),
        ]
    
        all_passed = True
        for timecode, expected in test_cases:
            try:
                result = TimecodeParser.parse_timecode(timecode)
                if abs(result - expected) < 0.001:  # 1ms tolerance
                    print(f"‚úì '{timecode}' -> {result:.3f}s (expected {expected:.3f}s)")
                else:
                    print(f"‚úó '{timecode}' -> {result:.3f}s (expected {expected:.3f}s)")
                    all_passed = False
            except Exception as e:
                print(f"‚úó '{timecode}' -> ERROR: {e}")
                all_passed = False
    
        return all_passed
    
    def test_format_precision():
        """Test that formatting preserves millisecond precision."""
        print("\nTesting TimecodeParser.format_seconds precision...")
    
        test_cases = [
            (17.000, "00:00:17"),
            (17.500, "00:00:17.500"),
            (17.050, "00:00:17.050"),
            (17.005, "00:00:17.005"),
            (90.250, "00:01:30.250"),
            (3600.001, "01:00:00.001"),
            (12.999, "00:00:12.999"),  # Test no accidental rounding up
        ]
    
        all_passed = True
        for seconds, expected in test_cases:
            try:
                result = TimecodeParser.format_seconds(seconds)
                if result == expected:
                    print(f"‚úì {seconds:.3f}s -> '{result}' (expected '{expected}')")
                else:
                    print(f"‚úó {seconds:.3f}s -> '{result}' (expected '{expected}')")
                    all_passed = False
            except Exception as e:
                print(f"‚úó {seconds:.3f}s -> ERROR: {e}")
                all_passed = False
    
        return all_passed
    
    def test_round_trip():
        """Test that parse -> format -> parse preserves precision."""
        print("\nTesting round-trip precision...")
    
        test_timecodes = [
            "00:00:17.5",
            "00:00:17.05",
            "00:00:17.500",
            "00:01:30.250",
            "01:00:00.001",
        ]
    
        all_passed = True
        for original in test_timecodes:
            try:
                # Parse -> format -> parse
                parsed1 = TimecodeParser.parse_timecode(original)
                formatted = TimecodeParser.format_seconds(parsed1)
                parsed2 = TimecodeParser.parse_timecode(formatted)
    
                if abs(parsed1 - parsed2) < 0.001:  # 1ms tolerance
                    print(f"‚úì '{original}' -> {parsed1:.3f}s -> '{formatted}' -> {parsed2:.3f}s")
                else:
                    print(f"‚úó '{original}' -> {parsed1:.3f}s -> '{formatted}' -> {parsed2:.3f}s (precision lost)")
                    all_passed = False
            except Exception as e:
                print(f"‚úó '{original}' -> ERROR: {e}")
                all_passed = False
    
        return all_passed
    
    def main():
        """Run all precision tests."""
        print("=" * 60)
        print("MILLISECOND PRECISION TEST SUITE")
        print("=" * 60)
    
        parse_ok = test_parse_precision()
        format_ok = test_format_precision()
        roundtrip_ok = test_round_trip()
    
        print("\n" + "=" * 60)
        if parse_ok and format_ok and roundtrip_ok:
            print("üéâ ALL TESTS PASSED - Millisecond precision is working correctly!")
            return 0
        else:
            print("‚ùå SOME TESTS FAILED - Precision issues detected")
            return 1
    
    if __name__ == "__main__":
        sys.exit(main())
```

---

#### `timecodes\2025.01.02-kl.12.52.18--Perspektiv_a_compressed.yaml`

```yaml
    # editing-optimized-example.yaml
    # Professional video editing workflow configuration
    
    input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.02-kl.12.52.18--Perspektiv_a_compressed.mp4"
    use_subdirectory: true
    output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.02"
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    
    # ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
    # ==============================================
    
    # RECOMMENDED: All-Intra H.264 for frame-accurate editing
    # - Every frame is a keyframe (I-frame) for precise cuts
    # - CRF 18 provides visually lossless quality
    # - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
    codec_profile: "h264_intra"
    output_format: "mp4"
    
    # QUALITY CONTROL FOR FILE SIZE
    # =============================
    # Quality setting (0-51): Controls file size vs quality trade-off
    # - 0: Lossless (largest files, perfect quality)
    # - 18: Visually lossless (default, excellent quality)
    # - 23: Standard quality (good balance)
    # - 28: Smaller files (good quality, noticeable compression)
    # - 35: Much smaller files (acceptable quality, visible artifacts)
    quality: 18
    
    # ALTERNATIVE: ProRes 422 HQ for professional workflows
    # Uncomment the lines below for ProRes output:
    # codec_profile: "prores_hq"
    # output_format: "mov"  # ProRes requires MOV container
    
    # ALTERNATIVE: Fast stream copy for quick previews (smallest files)
    # codec_profile: "fast_copy"
    # Note: fast_copy ignores quality setting
    
    # ALTERNATIVE: Legacy After Effects compatibility
    # codec_profile: "legacy_ae"
    
    threads: 4
    append_timestamps: true
    include_source_prefix: false
    
    # TECHNICAL NOTES:
    # ================
    # h264_intra settings:
    #   - All-Intra encoding (-g 1) ensures every frame is seekable
    #   - CRF 18 provides visually lossless quality
    #   - PCM audio avoids compression artifacts
    #   - BT.709 color space prevents color shifts
    #
    # prores_hq settings:
    #   - ProRes 422 HQ profile for high-quality intermediate files
    #   - Uncompressed PCM audio
    #   - Industry standard for professional editing
    #
    # fast_copy settings:
    #   - Stream copy for maximum speed
    #   - May have seeking issues on P-frames and B-frames
    #   - Best for quick previews or when frame accuracy isn't critical
    
    # TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
    # Examples:
    #   "00:00:17" = 17 seconds
    #   "00:04:44" = 4 minutes 44 seconds
    #   "00:00:17.500" = 17.5 seconds (500ms)
    #   "00:01:30.250" = 1 minute 30.25 seconds (250ms)
    
    segments:
      - start: "00:14:37.24"
        end: "00:17:57.25"
        title: "02-a-de som beveger seg rundt med lukkede øyne"
    
      - start: "00:18:15.24"
        end: "00:19:31.28"
        title: "02-b-ikke alt i livet man skal unngå"
    
      - start: "00:19:46.15"
        end: "00:19:52.24"
        title: "02-c-bare går sporene jeg har gått"
    
      - start: "00:20:00.26"
        end: "00:20:05.03"
        title: "02-d-suger til meg alt jeg kan"
    
      - start: "00:20:11.16"
        end: "00:22:08.06"
        title: "02-e-hva som er mistet"
```

---

#### `timecodes\2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.yaml`

```yaml
    # editing-optimized-example.yaml
    # Professional video editing workflow configuration
    
    input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
    use_subdirectory: true
    output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22"
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    
    # ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
    # ==============================================
    
    # RECOMMENDED: All-Intra H.264 for frame-accurate editing
    # - Every frame is a keyframe (I-frame) for precise cuts
    # - CRF 18 provides visually lossless quality
    # - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
    codec_profile: "h264_intra"
    output_format: "mp4"
    
    # QUALITY CONTROL FOR FILE SIZE
    # =============================
    # Quality setting (0-51): Controls file size vs quality trade-off
    # - 0: Lossless (largest files, perfect quality)
    # - 18: Visually lossless (default, excellent quality)
    # - 23: Standard quality (good balance)
    # - 28: Smaller files (good quality, noticeable compression)
    # - 35: Much smaller files (acceptable quality, visible artifacts)
    quality: 18
    
    # ALTERNATIVE: ProRes 422 HQ for professional workflows
    # Uncomment the lines below for ProRes output:
    # codec_profile: "prores_hq"
    # output_format: "mov"  # ProRes requires MOV container
    
    # ALTERNATIVE: Fast stream copy for quick previews (smallest files)
    # codec_profile: "fast_copy"
    # Note: fast_copy ignores quality setting
    
    # ALTERNATIVE: Legacy After Effects compatibility
    # codec_profile: "legacy_ae"
    
    threads: 4
    append_timestamps: true
    include_source_prefix: false
    
    # TECHNICAL NOTES:
    # ================
    # h264_intra settings:
    #   - All-Intra encoding (-g 1) ensures every frame is seekable
    #   - CRF 18 provides visually lossless quality
    #   - PCM audio avoids compression artifacts
    #   - BT.709 color space prevents color shifts
    #
    # prores_hq settings:
    #   - ProRes 422 HQ profile for high-quality intermediate files
    #   - Uncompressed PCM audio
    #   - Industry standard for professional editing
    #
    # fast_copy settings:
    #   - Stream copy for maximum speed
    #   - May have seeking issues on P-frames and B-frames
    #   - Best for quick previews or when frame accuracy isn't critical
    
    # TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
    # Examples:
    #   "00:00:17" = 17 seconds
    #   "00:04:44" = 4 minutes 44 seconds
    #   "00:00:17.500" = 17.5 seconds (500ms)
    #   "00:01:30.250" = 1 minute 30.25 seconds (250ms)
    
    segments:
      - start: "00:00:00:00"
        end: "00:00:17:00"
        title: "01-a-starte videoopptak"
    
      - start: "00:00:34:00"
        end: "00:03:11:00"
        title: "02-b-1-meg-dele perspektiv"
    
      - start: "00:03:11:00"
        end: "00:04:44:00"
        title: "02-b-2-hva-se hverandre"
    
      - start: "00:04:54:00"
        end: "00:06:43:12"
        title: "03-c-hvorfor-det uperfekte, hånd i hånd"
    
      - start: "00:07:11:16"
        end: "00:07:39:03"
        title: "04-d1-jeg navigerer ustødig"
    
      - start: "00:07:42:12"
        end: "00:08:05:15"
        title: "05-d2-men jeg tror egentlig alle gjør det litt"
    
      - start: "00:08:10:11"
        end: "00:09:38:29"
        title: "06-d3-de som stjeler oppmerksomheten vår"
    
      - start: "00:09:42:27"
        end: "00:09:57:17"
        title: "07-d4-oppmerksomheten vår"
    
      - start: "00:10:06:22"
        end: "00:10:23:17"
        title: "07-d5-tard"
    
      - start: "00:10:34:10"
        end: "00:10:47:10"
        title: "08-d6-jeg er ikke klar til å sende ut en beskjed"
    
      - start: "00:10:47:10"
        end: "00:12:57:26"
        title: "08-d7-de som ikke snakker sant"
    
      - start: "00:13:03:19"
        end: "00:13:14:28"
        title: "09-d8-jeg har så mye kritikk"
    
      - start: "00:13:26:09"
        end: "00:13:40:03"
        title: "10-politikere-mennesker med makt"
    
      - start: "00:13:51:09"
        end: "00:14:32:10"
        title: "11-politikere-blir farget av markedet"
    
      - start: "00:14:32:10"
        end: "00:15:14:20"
        title: "12-kaos, man blir aldri helt fri fra det"
    
      - start: "00:15:14:20"
        end: "00:16:27:17"
        title: "13-utsikten fra vinduet på røyse"
    
      - start: "00:19:45:12"
        end: "00:21:35:25"
        title: "14-a-la oss være mennesker sammen"
    
      - start: "00:21:35:25"
        end: "00:21:47:20"
        title: "14-b-meg-du ser den som hjelper deg"
    
      - start: "00:21:47:20"
        end: "00:23:11:03"
        title: "14-c-kritikk vi alle fortjener"
    
      - start: "00:23:11:03"
        end: "00:23:31:21"
        title: "14-d-meg-det er vi gir oss selv alle oppgavene"
    
      - start: "00:23:33:12"
        end: "00:24:13:27"
        title: "14-e-meg-jeg er som en hund som ser en ball"
    
      - start: "00:24:13:27"
        end: "00:24:48:12"
        title: "14-f-uendelighet av det du velger"
    
      - start: "00:24:53:23"
        end: "00:24:59:22"
        title: "14-g-i uendelighet så finnes kjærlighet"
    
      - start: "00:24:59:22"
        end: "00:25:27:16"
        title: "15-a-det er mange rikdommer å finne i livet"
    
      - start: "00:25:27:16"
        end: "00:25:50:18"
        title: "15-b-det trenger aldri å bli helt svart"
    
      - start: "00:25:50:18"
        end: "00:26:07:11"
        title: "16-a-meg-jeg kommer aldri til å si det rette"
    
      - start: "00:26:11:07"
        end: "00:26:25:00"
        title: "16-b-meg-ikke en som får til ting på første forsøk"
    
      - start: "00:26:25:00"
        end: "00:26:51:21"
        title: "16-c-meg-meditasjon, 10 år på å lure meg selv"
    
      - start: "00:26:51:21"
        end: "00:27:38:07"
        title: "16-d-meg-meditasjon, healer meg"
    
      - start: "00:27:38:07"
        end: "00:28:00:08"
        title: "16-e-meg-meditasjon, gi deg selv kjærlighet"
    
      - start: "00:28:00:08"
        end: "00:28:21:21"
        title: "17-a-for at verden skal gå i riktig retning"
    
      - start: "00:28:21:21"
        end: "00:28:30:08"
        title: "17-b-jeg tørr, jeg tard"
    
      - start: "00:28:30:08"
        end: "00:28:42:18"
        title: "17-c-må vi tørre å være rare"
    
      - start: "00:28:42:18"
        end: "00:29:04:00"
        title: "17-d-det som kommer etter"
    
      - start: "00:29:04:00"
        end: "00:29:17:05"
        title: "17-e-jeg ønsker ikke dra ned noen"
    
      - start: "00:29:17:05"
        end: "00:29:49:21"
        title: "17-e-men alle er rare"
    
      - start: "00:29:53:29"
        end: "00:30:16:15"
        title: "18-a-jeg kommer bare til å dele det"
    
      - start: "00:30:16:15"
        end: "00:30:49:10"
        title: "18-b-jeg legger ut brødsmulene"
```

---

#### `timecodes\2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.yaml.001`

```001
    # editing-optimized-example.yaml
    # Professional video editing workflow configuration
    
    input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
    use_subdirectory: true
    output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22"
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    
    # ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
    # ==============================================
    
    # RECOMMENDED: All-Intra H.264 for frame-accurate editing
    # - Every frame is a keyframe (I-frame) for precise cuts
    # - CRF 18 provides visually lossless quality
    # - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
    codec_profile: "h264_intra"
    output_format: "mp4"
    
    # QUALITY CONTROL FOR FILE SIZE
    # =============================
    # Quality setting (0-51): Controls file size vs quality trade-off
    # - 0: Lossless (largest files, perfect quality)
    # - 18: Visually lossless (default, excellent quality)
    # - 23: Standard quality (good balance)
    # - 28: Smaller files (good quality, noticeable compression)
    # - 35: Much smaller files (acceptable quality, visible artifacts)
    quality: 18
    
    # ALTERNATIVE: ProRes 422 HQ for professional workflows
    # Uncomment the lines below for ProRes output:
    # codec_profile: "prores_hq"
    # output_format: "mov"  # ProRes requires MOV container
    
    # ALTERNATIVE: Fast stream copy for quick previews (smallest files)
    # codec_profile: "fast_copy"
    # Note: fast_copy ignores quality setting
    
    # ALTERNATIVE: Legacy After Effects compatibility
    # codec_profile: "legacy_ae"
    
    threads: 4
    append_timestamps: true
    include_source_prefix: false
    
    # TECHNICAL NOTES:
    # ================
    # h264_intra settings:
    #   - All-Intra encoding (-g 1) ensures every frame is seekable
    #   - CRF 18 provides visually lossless quality
    #   - PCM audio avoids compression artifacts
    #   - BT.709 color space prevents color shifts
    #
    # prores_hq settings:
    #   - ProRes 422 HQ profile for high-quality intermediate files
    #   - Uncompressed PCM audio
    #   - Industry standard for professional editing
    #
    # fast_copy settings:
    #   - Stream copy for maximum speed
    #   - May have seeking issues on P-frames and B-frames
    #   - Best for quick previews or when frame accuracy isn't critical
    
    # TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
    # Examples:
    #   "00:00:17" = 17 seconds
    #   "00:04:44" = 4 minutes 44 seconds
    #   "00:00:17.500" = 17.5 seconds (500ms)
    #   "00:01:30.250" = 1 minute 30.25 seconds (250ms)
    
    segments:
      - start: "00:00:00.00"
        end: "00:00:17.00"
        title: "01-a-starte videoopptak"
    
      - start: "00:00:34.00"
        end: "00:04:44.00"
        title: "02-b-dele perspektiv"
    
      - start: "00:04:54.00"
        end: "00:06:43.20"
        title: "03-c-det uperfekte, hånd i hånd"
    
      - start: "00:07:01.16"
        end: "00:07:39.00"
        title: "04-d1-oppmerksomheten vår"
    
      - start: "00:07:42.12"
        end: "00:08:05.15"
        title: "05-d2-oppmerksomheten vår"
    
      - start: "00:08:10.11"
        end: "00:09:38.29"
        title: "06-d3-oppmerksomheten vår"
    
      - start: "00:09:42.27"
        end: "00:10:25.26"
        title: "07-d4-oppmerksomheten vår"
    
      - start: "00:10:24.29"
        end: "00:12:58.10"
        title: "08-d5-oppmerksomheten vår"
    
      - start: "00:13:01.26"
        end: "00:13:14.28"
        title: "09-d5-oppmerksomheten vår"
    
      - start: "00:13:26.09"
        end: "00:13:40.03"
        title: "10-politikere-mennesker med makt"
    
      - start: "00:13:51.09"
        end: "00:14:32.10"
        title: "11-politikere-blir farget av markedet"
    
      - start: "00:14:32.10"
        end: "00:15:14.20"
        title: "12-kaos, man blir aldri helt fri fra det"
    
      - start: "00:15:14.20"
        end: "00:16:27.17"
        title: "13-utsikten fra vinduet på røyse"
    
      - start: "00:19:45.12"
        end: "00:24:48.12"
        title: "14-la oss være mennesker sammen"
    
      - start: "00:24:50.27"
        end: "00:25:50.18"
        title: "15-trenger aldri å bli helt svart "
    
      - start: "00:25:50.18"
        end: "00:28:00.08"
        title: "16-meditasjon, lurer meg selv"
    
      #
      - start: "00:28:00.08"
        end: "00:29:49.21"
        title: "17-vi må tørre å være rare"
    
      - start: "00:30:02.26"
        end: "00:30:53.17"
        title: "18-jeg legger ut brødsmulene"
```

---

#### `timecodes\2025.08.17-kl.09.41.00--Achalasia.yaml`

```yaml
    # editing-optimized-example.yaml
    # Professional video editing workflow configuration
    
    input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_helse/videos/NA-Top Doctors UK-A guide to hiatal hernias - Online interview.mkv"
    use_subdirectory: true
    output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_helse/videos"
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    
    # ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
    # ==============================================
    
    # RECOMMENDED: All-Intra H.264 for frame-accurate editing
    # - Every frame is a keyframe (I-frame) for precise cuts
    # - CRF 18 provides visually lossless quality
    # - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
    codec_profile: "h264_intra"
    output_format: "mp4"
    
    # QUALITY CONTROL FOR FILE SIZE
    # =============================
    # Quality setting (0-51): Controls file size vs quality trade-off
    # - 0: Lossless (largest files, perfect quality)
    # - 18: Visually lossless (default, excellent quality)
    # - 23: Standard quality (good balance)
    # - 28: Smaller files (good quality, noticeable compression)
    # - 35: Much smaller files (acceptable quality, visible artifacts)
    quality: 18
    
    # ALTERNATIVE: ProRes 422 HQ for professional workflows
    # Uncomment the lines below for ProRes output:
    # codec_profile: "prores_hq"
    # output_format: "mov"  # ProRes requires MOV container
    
    # ALTERNATIVE: Fast stream copy for quick previews (smallest files)
    # codec_profile: "fast_copy"
    # Note: fast_copy ignores quality setting
    
    # ALTERNATIVE: Legacy After Effects compatibility
    # codec_profile: "legacy_ae"
    
    threads: 4
    append_timestamps: true
    include_source_prefix: false
    
    # TECHNICAL NOTES:
    # ================
    # h264_intra settings:
    #   - All-Intra encoding (-g 1) ensures every frame is seekable
    #   - CRF 18 provides visually lossless quality
    #   - PCM audio avoids compression artifacts
    #   - BT.709 color space prevents color shifts
    #
    # prores_hq settings:
    #   - ProRes 422 HQ profile for high-quality intermediate files
    #   - Uncompressed PCM audio
    #   - Industry standard for professional editing
    #
    # fast_copy settings:
    #   - Stream copy for maximum speed
    #   - May have seeking issues on P-frames and B-frames
    #   - Best for quick previews or when frame accuracy isn't critical
    
    # TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
    # Examples:
    #   "00:00:17" = 17 seconds
    #   "00:04:44" = 4 minutes 44 seconds
    #   "00:00:17.500" = 17.5 seconds (500ms)
    #   "00:01:30.250" = 1 minute 30.25 seconds (250ms)
    
    segments:
      - start: "00:14:25:18"
        end: "00:18:16:18"
        title: "achalasia"
```

---

#### `timecodes\2025.08.22-kl.22.56--southpark_s27e03.yaml`

```yaml
    # editing-optimized-example.yaml
    # Professional video editing workflow configuration
    
    input_video: "C:/Users/<USER>/Desktop/my/flow/downloads/South Park S27E03 Sickofancy.mkv"
    use_subdirectory: true
    output_dir: "C:/Users/<USER>/Desktop/my/flow/android/share"
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    
    # ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
    # ==============================================
    
    # RECOMMENDED: All-Intra H.264 for frame-accurate editing
    # - Every frame is a keyframe (I-frame) for precise cuts
    # - CRF 18 provides visually lossless quality
    # - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
    codec_profile: "h264_intra"
    output_format: "mp4"
    
    # QUALITY CONTROL FOR FILE SIZE
    # =============================
    # Quality setting (0-51): Controls file size vs quality trade-off
    # - 0: Lossless (largest files, perfect quality)
    # - 18: Visually lossless (default, excellent quality)
    # - 23: Standard quality (good balance)
    # - 28: Smaller files (good quality, noticeable compression)
    # - 35: Much smaller files (acceptable quality, visible artifacts)
    quality: 18
    
    # ALTERNATIVE: ProRes 422 HQ for professional workflows
    # Uncomment the lines below for ProRes output:
    # codec_profile: "prores_hq"
    # output_format: "mov"  # ProRes requires MOV container
    
    # ALTERNATIVE: Fast stream copy for quick previews (smallest files)
    # codec_profile: "fast_copy"
    # Note: fast_copy ignores quality setting
    
    # ALTERNATIVE: Legacy After Effects compatibility
    # codec_profile: "legacy_ae"
    
    threads: 4
    append_timestamps: true
    include_source_prefix: false
    
    # TECHNICAL NOTES:
    # ================
    # h264_intra settings:
    #   - All-Intra encoding (-g 1) ensures every frame is seekable
    #   - CRF 18 provides visually lossless quality
    #   - PCM audio avoids compression artifacts
    #   - BT.709 color space prevents color shifts
    #
    # prores_hq settings:
    #   - ProRes 422 HQ profile for high-quality intermediate files
    #   - Uncompressed PCM audio
    #   - Industry standard for professional editing
    #
    # fast_copy settings:
    #   - Stream copy for maximum speed
    #   - May have seeking issues on P-frames and B-frames
    #   - Best for quick previews or when frame accuracy isn't critical
    
    # TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
    # Examples:
    #   "00:00:17" = 17 seconds
    #   "00:04:44" = 4 minutes 44 seconds
    #   "00:00:17.500" = 17.5 seconds (500ms)
    #   "00:01:30.250" = 1 minute 30.25 seconds (250ms)
    
    segments:
      - start: "00:04:50:06"
        end: "00:05:16:17"
        title: "01"
    
      - start: "00:05:16:17"
        end: "00:05:31:16"
        title: "02"
    
      - start: "00:05:33:09"
        end: "00:05:48:18"
        title: "03"
    
      - start: "00:06:20:02"
        end: "00:07:19:18"
        title: "04_inahole"
    
      - start: "00:07:28:01"
        end: "00:08:29:08"
        title: "05"
    
      - start: "00:10:24:20"
        end: "00:11:28:04"
        title: "06"
    
      - start: "00:16:42:14"
        end: "00:17:25:22"
        title: "07_inahole"
    
      - start: "00:17:32:23"
        # end: "00:18:58:21"
        end: "00:19:12:04"
        title: "08"
```

---

#### `timecodes\editing-optimized-example.yaml`

```yaml
    # editing-optimized-example.yaml
    # Professional video editing workflow configuration
    
    input_video: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
    use_subdirectory: true
    output_dir: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/video_splitter/timecodes"
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    
    # ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
    # ==============================================
    
    # RECOMMENDED: All-Intra H.264 for frame-accurate editing
    # - Every frame is a keyframe (I-frame) for precise cuts
    # - CRF 18 provides visually lossless quality
    # - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
    codec_profile: "h264_intra"
    output_format: "mp4"
    
    # QUALITY CONTROL FOR FILE SIZE
    # =============================
    # Quality setting (0-51): Controls file size vs quality trade-off
    # - 0: Lossless (largest files, perfect quality)
    # - 18: Visually lossless (default, excellent quality)
    # - 23: Standard quality (good balance)
    # - 28: Smaller files (good quality, noticeable compression)
    # - 35: Much smaller files (acceptable quality, visible artifacts)
    quality: 18
    
    # ALTERNATIVE: ProRes 422 HQ for professional workflows
    # Uncomment the lines below for ProRes output:
    # codec_profile: "prores_hq"
    # output_format: "mov"  # ProRes requires MOV container
    
    # ALTERNATIVE: Fast stream copy for quick previews (smallest files)
    # codec_profile: "fast_copy"
    # Note: fast_copy ignores quality setting
    
    # ALTERNATIVE: Legacy After Effects compatibility
    # codec_profile: "legacy_ae"
    
    threads: 4
    append_timestamps: true
    include_source_prefix: false
    
    # TECHNICAL NOTES:
    # ================
    # h264_intra settings:
    #   - All-Intra encoding (-g 1) ensures every frame is seekable
    #   - CRF 18 provides visually lossless quality
    #   - PCM audio avoids compression artifacts
    #   - BT.709 color space prevents color shifts
    #
    # prores_hq settings:
    #   - ProRes 422 HQ profile for high-quality intermediate files
    #   - Uncompressed PCM audio
    #   - Industry standard for professional editing
    #
    # fast_copy settings:
    #   - Stream copy for maximum speed
    #   - May have seeking issues on P-frames and B-frames
    #   - Best for quick previews or when frame accuracy isn't critical
    
    # TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
    # Examples:
    #   "00:00:17" = 17 seconds
    #   "00:04:44" = 4 minutes 44 seconds
    #   "00:00:17.500" = 17.5 seconds (500ms)
    #   "00:01:30.250" = 1 minute 30.25 seconds (250ms)
    
    segments:
      - start: "00:00:00.00"
        end: "00:00:17.00"
        title: "01-a-starte videoopptak"
    
      - start: "00:00:34.00"
        end: "00:04:44.00"
        title: "02-b-dele perspektiv"
    
      - start: "00:04:54.00"
        end: "00:06:43.20"
        title: "03-c-det uperfekte, hånd i hånd"
    
      - start: "00:07:01.16"
        end: "00:07:39.00"
        title: "04-d1-oppmerksomheten vår"
    
      - start: "00:07:42.12"
        end: "00:08:05.15"
        title: "05-d2-oppmerksomheten vår"
    
      - start: "00:08:10.11"
        end: "00:09:38.29"
        title: "06-d3-oppmerksomheten vår"
    
      - start: "00:09:42.27"
        end: "00:10:25.26"
        title: "07-d4-oppmerksomheten vår"
    
      - start: "00:10:24.29"
        end: "00:12:58.10"
        title: "08-d5-oppmerksomheten vår"
    
      - start: "00:13:01.26"
        end: "00:13:14.28"
        title: "09-d5-oppmerksomheten vår"
    
      - start: "00:13:26.09"
        end: "00:13:40.03"
        title: "10-politikere-mennesker med makt"
    
      - start: "00:13:51.09"
        end: "00:14:32.10"
        title: "11-politikere-blir farget av markedet"
    
      - start: "00:14:32.10"
        end: "00:15:14.20"
        title: "12-kaos, man blir aldri helt fri fra det"
    
      - start: "00:15:14.20"
        end: "00:16:27.17"
        title: "13-utsikten fra vinduet på røyse"
    
      - start: "00:19:45.12"
        end: "00:24:48.12"
        title: "14-la oss være mennesker sammen"
    
      - start: "00:24:50.27"
        end: "00:25:50.18"
        title: "15-trenger aldri å bli helt svart "
    
      - start: "00:25:50.18"
        end: "00:28:00.08"
        title: "16-meditasjon, lurer meg selv"
    
      #
      - start: "00:28:00.08"
        end: "00:29:49.21"
        title: "17-vi må tørre å være rare"
    
      - start: "00:30:02.26"
        end: "00:30:53.17"
        title: "18-jeg legger ut brødsmulene"
```

---

#### `timecodes\input-timecodes.yaml`

```yaml
    # input-timecodes.yaml
    # Sample configuration file for Video Splitter
    
    input_video: "sample_video.mp4"
    use_subdirectory: true
    output_dir: "output_segments"
    
    ffmpeg_path: "ffmpeg"
    ffprobe_path: "ffprobe"
    output_format: "mp4"
    
    # ENCODING PROFILE OPTIONS:
    # fast_copy    - Stream copy (fastest, may have seeking issues)
    # h264_intra   - All-Intra H.264 (frame-accurate, editing-optimized)
    # prores_hq    - ProRes 422 HQ (professional mezzanine, requires .mov)
    # legacy_ae    - Legacy After Effects compatibility
    codec_profile: "h264_intra"
    
    # QUALITY CONTROL FOR FILE SIZE
    # Quality setting (0-51): Controls file size vs quality trade-off
    # - 0: Lossless (largest files, perfect quality)
    # - 18: Visually lossless (default, excellent quality)
    # - 23: Standard quality (good balance)
    # - 28: Smaller files (good quality, noticeable compression)
    # - 35: Much smaller files (acceptable quality, visible artifacts)
    quality: 18
    
    threads: 4
    append_timestamps: true
    include_source_prefix: true
    
    # TIMECODE FORMAT GUIDE:
    # Use HH:MM:SS or HH:MM:SS.ms format for unambiguous interpretation:
    #   "00:00:17"       = 17 seconds
    #   "00:01:30"       = 1 minute 30 seconds
    #   "00:00:17.500"   = 17.5 seconds (500ms)
    #   "00:01:30.250"   = 1 minute 30.25 seconds (250ms)
    #   "01:30:45"       = 1 hour 30 minutes 45 seconds
    
    segments:
      - start: "00:00:46"
        end: "00:01:16"
        title: "Introduction"
    
      - start: "00:01:17"
        end: "00:05:17"
        title: "Main Content"
    
      - start: "00:08:21"
        end: "00:11:12"
        title: "Conclusion"
```

