#!/usr/bin/env python3
"""
Test script to verify millisecond precision fixes in TimecodeParser.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'video_splitter', 'src'))

from main import TimecodeParser

def test_parse_precision():
    """Test that parsing handles fractional seconds correctly."""
    print("Testing TimecodeParser.parse_timecode precision...")

    test_cases = [
        ("00:00:17", 17.000),
        ("00:00:17.5", 17.500),      # Should be 500ms, not 5ms
        ("00:00:17.05", 17.050),     # Should be 50ms, not 5ms
        ("00:00:17.500", 17.500),
        ("00:01:30.250", 90.250),
        ("01:00:00.001", 3600.001),
    ]

    all_passed = True
    for timecode, expected in test_cases:
        try:
            result = TimecodeParser.parse_timecode(timecode)
            if abs(result - expected) < 0.001:  # 1ms tolerance
                print(f"✓ '{timecode}' -> {result:.3f}s (expected {expected:.3f}s)")
            else:
                print(f"✗ '{timecode}' -> {result:.3f}s (expected {expected:.3f}s)")
                all_passed = False
        except Exception as e:
            print(f"✗ '{timecode}' -> ERROR: {e}")
            all_passed = False

    return all_passed

def test_format_precision():
    """Test that formatting preserves millisecond precision."""
    print("\nTesting TimecodeParser.format_seconds precision...")

    test_cases = [
        (17.000, "00:00:17"),
        (17.500, "00:00:17.500"),
        (17.050, "00:00:17.050"),
        (17.005, "00:00:17.005"),
        (90.250, "00:01:30.250"),
        (3600.001, "01:00:00.001"),
        (12.999, "00:00:12.999"),  # Test no accidental rounding up
    ]

    all_passed = True
    for seconds, expected in test_cases:
        try:
            result = TimecodeParser.format_seconds(seconds)
            if result == expected:
                print(f"✓ {seconds:.3f}s -> '{result}' (expected '{expected}')")
            else:
                print(f"✗ {seconds:.3f}s -> '{result}' (expected '{expected}')")
                all_passed = False
        except Exception as e:
            print(f"✗ {seconds:.3f}s -> ERROR: {e}")
            all_passed = False

    return all_passed

def test_round_trip():
    """Test that parse -> format -> parse preserves precision."""
    print("\nTesting round-trip precision...")

    test_timecodes = [
        "00:00:17.5",
        "00:00:17.05",
        "00:00:17.500",
        "00:01:30.250",
        "01:00:00.001",
    ]

    all_passed = True
    for original in test_timecodes:
        try:
            # Parse -> format -> parse
            parsed1 = TimecodeParser.parse_timecode(original)
            formatted = TimecodeParser.format_seconds(parsed1)
            parsed2 = TimecodeParser.parse_timecode(formatted)

            if abs(parsed1 - parsed2) < 0.001:  # 1ms tolerance
                print(f"✓ '{original}' -> {parsed1:.3f}s -> '{formatted}' -> {parsed2:.3f}s")
            else:
                print(f"✗ '{original}' -> {parsed1:.3f}s -> '{formatted}' -> {parsed2:.3f}s (precision lost)")
                all_passed = False
        except Exception as e:
            print(f"✗ '{original}' -> ERROR: {e}")
            all_passed = False

    return all_passed

def main():
    """Run all precision tests."""
    print("=" * 60)
    print("MILLISECOND PRECISION TEST SUITE")
    print("=" * 60)

    parse_ok = test_parse_precision()
    format_ok = test_format_precision()
    roundtrip_ok = test_round_trip()

    print("\n" + "=" * 60)
    if parse_ok and format_ok and roundtrip_ok:
        print("🎉 ALL TESTS PASSED - Millisecond precision is working correctly!")
        return 0
    else:
        print("❌ SOME TESTS FAILED - Precision issues detected")
        return 1

if __name__ == "__main__":
    sys.exit(main())
