# Video Splitter

## Overview
Video segmentation tool with FFmpeg integration and rich CLI interface, supporting precise time-based video splitting with YAML/CSV configuration.

## Features
- **Strict timecode format**: HH:MM:SS or HH:MM:SS.ms for unambiguous interpretation
- **YAML/CSV configuration**: Load segments and settings from configuration files
- **Interactive CLI**: User-friendly prompts with Rich-based interface
- **Concurrent processing**: Multi-threaded segment extraction for faster processing
- **Professional encoding profiles**: Optimized settings for video editing workflows
- **Frame-accurate cutting**: All-Intra H.264 and ProRes support for precise editing
- **Comprehensive validation**: Overlap detection and duration checking
- **Progress tracking**: Real-time processing progress with visual indicators
- **Flexible output naming**: Timestamp appending and source prefix options

## Quick Start
Run `run.bat` to start the interactive video splitter (handles environment setup automatically)

## Usage
```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py --prompt
uv run python src/main.py -i "video.mp4" -op "output/" -tc "timecodes.yaml"
```

## Configuration File Example
```yaml
# timecodes.yaml
input_video: "path/to/video.mp4"
output_dir: "output/segments"
use_subdirectory: true

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"
output_format: "mp4"

# Encoding profile for optimal video editing workflows
# Options: fast_copy, h264_intra, prores_hq, legacy_ae
codec_profile: "h264_intra"

# Quality control for file size (0-51)
# - 0: Lossless (largest files)
# - 18: Visually lossless (default)
# - 23: Standard quality
# - 28: Smaller files
# - 35: Much smaller files
quality: 18

threads: 4
append_timestamps: true
include_source_prefix: false

# Timecode format: HH:MM:SS or HH:MM:SS.ms
segments:
  - start: "00:00:46"
    end: "00:01:16"
    title: "Introduction"
  - start: "00:01:17"
    end: "00:05:17"
    title: "Main Content"
  - start: "00:08:21"
    end: "00:11:12"
    title: "Conclusion"
```

## Timecode Format
The tool uses a strict HH:MM:SS or HH:MM:SS.ms format for unambiguous interpretation:

- `"00:00:17"` = 17 seconds
- `"00:04:44"` = 4 minutes 44 seconds
- `"00:00:17.500"` = 17.5 seconds (500ms)
- `"00:01:30.250"` = 1 minute 30.25 seconds (250ms)
- `"01:30:45"` = 1 hour 30 minutes 45 seconds

## Requirements
- **FFmpeg**: Required for video processing (must be in PATH or specify path)
- **FFprobe**: Required for video metadata extraction (usually comes with FFmpeg)

## Dependencies
Managed via `pyproject.toml` with uv package manager.

## Encoding Profiles

### `fast_copy` (Default)
- **Use case**: Quick extraction when frame accuracy isn't critical
- **Method**: Stream copy (no re-encoding)
- **Pros**: Fastest processing, smallest files
- **Cons**: May have seeking issues in editors, cuts only on keyframes

### `h264_intra` (Recommended for Editing)
- **Use case**: Frame-accurate editing in Premiere Pro, DaVinci Resolve, etc.
- **Method**: All-Intra H.264 with visually lossless quality (CRF 18)
- **Pros**: Frame-accurate cuts, smooth scrubbing, excellent quality
- **Cons**: Larger file sizes than fast_copy

### `prores_hq` (Professional)
- **Use case**: Professional editing workflows, color grading, high-end post-production
- **Method**: ProRes 422 HQ with 10-bit 4:2:2 color sampling and 24-bit audio
- **Technical**: `yuv422p10le`, `pcm_s24le`, CFR sync, optimized GOP structure
- **Pros**: Industry standard, maximum quality, best editing performance
- **Cons**: Very large files, requires .mov container

### `legacy_ae` (Compatibility)
- **Use case**: Older After Effects versions or compatibility issues
- **Method**: Standard H.264 with AAC audio
- **Pros**: Wide compatibility
- **Cons**: Not optimized for editing performance

## Command Line Options
- `-i, --input_video`: Path to input video file
- `-op, --output_path`: Output directory path
- `-tc, --timecodes_file`: Path to YAML/CSV timecodes file
- `--ffmpeg_path`: Path to ffmpeg executable (default: "ffmpeg")
- `--ffprobe_path`: Path to ffprobe executable (default: "ffprobe")
- `--output_format`: Output video format (default: "mp4")
- `--codec_profile`: Encoding profile (fast_copy, h264_intra, prores_hq, legacy_ae)
- `--quality`: Quality setting 0-51 (default: 18, lower = smaller files)
- `--threads`: Number of concurrent threads (default: 1)
- `--append_timestamps`: Append timestamps to output filenames
- `--include_source_prefix`: Include source filename as prefix
- `--use_subdirectory`: Create subdirectory for output files
- `--prompt`: Interactive mode
