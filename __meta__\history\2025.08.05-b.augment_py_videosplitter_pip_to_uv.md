

<!-- ======================================================= -->
<!-- [2025.08.05 14:20] -->
Please familiarize yourself with the current @codebase; `py__VideoSplitter` in particular. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.



<!-- ======================================================= -->
<!-- [2025.08.05 14:23] -->

Please thoroughly familiarize yourself with those inside `\converted\`—your primary imperative is to understand these so deeply that you could concistently and confidently make changes as if you were its original architect, then prepare yourself for replacing pip with uv for `py__VideoSplitter` (inside \todo\). The intent is not only to replace pip with uv, but also to make sure each of the updated projects' dirtree is clean and concistent (similar to those already converted). Each of the projects should have an interactive cli (similar to those already there, where if called with the --prompt argument they will prompt the user for all arguments interactively instead of just failing). The interactive prompt should always be triggered if there are missing arguments, but if called with --prompt it should interactively prompt user for each argument (if argument is also passed in they should be used as default in the interactive prompt, selected if user doesnt override). You must also ensure that unused packages is stripped away/removed, and that you adhere to the principles of concistency, simplicity and elegance (over unneccessary complexity, bloat and redundancy).

<!-- ======================================================= -->
<!-- [2025.08.05 14:49] -->

you can use the original @c:\Users\<USER>\Desktop\__SCRATCH__\2025.08.05-kl.14.12--pip_to_uv\pip_to_uv/todo\py__VideoSplitter\src\main.py 's (todo\py__VideoSplitter\src\main.py) VideoSplitterCLI class as reference, but make sure that you do the updated work in @c:\Users\<USER>\Desktop\__SCRATCH__\2025.08.05-kl.14.12--pip_to_uv\pip_to_uv/converted\py__VideoSplitter\video_splitter\src\main.py (converted\py__VideoSplitter\video_splitter\src\main.py) which has been upgraded to use uv