# input-timecodes.yaml
# Sample configuration file for Video Splitter

input_video: "sample_video.mp4"
use_subdirectory: true
output_dir: "output_segments"

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"
output_format: "mp4"
codec: "libx264"  # Use "libx264" for frame-accurate seeking, "copy" for speed
threads: 4
after_effects_compatible: false
append_timestamps: true
include_source_prefix: true

# TIMECODE FORMAT GUIDE:
# Use explicit H:MM:SS.mmm format to avoid ambiguity
# Examples:
#   0:00:17.30  = 17.30 seconds
#   0:04:41.14  = 4 minutes 41.14 seconds
#   1:30:45.00  = 1 hour 30 minutes 45 seconds

segments:
  - start: "0:00:46.00"
    end: "0:01:16.00"
    title: "Introduction"

  - start: "0:01:17.00"
    end: "0:05:17.00"
    title: "Main Content"

  - start: "0:08:21.00"
    end: "0:11:12.00"
    title: "Conclusion"
