#!/usr/bin/env python3
"""
Test the actual case from your YAML file
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from main import TimecodeParser

def test_actual_yaml_case():
    """Test the actual values from your YAML file."""
    
    print("=== Testing Your Actual YAML Values ===")
    
    # From your YAML file:
    # start: "00:06"
    # end: "00:17"
    
    test_cases = [
        ("00:06", "Start time from YAML"),
        ("00:17", "End time from YAML"),
        ("00:17:30", "Your desired format (17.30 seconds)"),
    ]
    
    for time_str, description in test_cases:
        print(f"\nTesting: '{time_str}' ({description})")
        
        try:
            # Test normalization
            normalized = TimecodeParser.normalize_timecode(time_str)
            print(f"  Normalized: '{normalized}'")
            
            # Test parsing
            seconds = TimecodeParser.parse_timecode(time_str)
            print(f"  Parsed: {seconds} seconds")
            
            # Convert to minutes for clarity
            minutes = seconds / 60
            print(f"  In minutes: {minutes:.2f} minutes")
            
            # Interpretation
            if time_str == "00:17":
                if seconds == 17.0:
                    print("  ✓ Correctly interpreted as 17 seconds")
                elif seconds == 17*60:
                    print("  ✗ Incorrectly interpreted as 17 minutes")
                else:
                    print(f"  ? Unexpected interpretation: {seconds} seconds")
                    
        except Exception as e:
            print(f"  ERROR: {e}")

def show_recommendations():
    """Show format recommendations."""
    
    print("\n=== Format Recommendations ===")
    print("If you want 17.30 seconds (17 seconds + 30 milliseconds):")
    print("  Option 1: '17.30'       (clearest)")
    print("  Option 2: '00:17:30'    (auto-detected)")
    print("  Option 3: '0:00:17.30'  (explicit)")
    print()
    print("If you want 17 seconds exactly:")
    print("  Option 1: '17'          (clearest)")
    print("  Option 2: '00:17'       (current format)")
    print("  Option 3: '0:00:17'     (explicit)")

if __name__ == "__main__":
    test_actual_yaml_case()
    show_recommendations()
