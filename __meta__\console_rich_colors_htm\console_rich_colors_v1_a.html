<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>

<title>Terminal Output Replica</title>
<style>
    body {
        background: #000000; /* black background */
        color: #ffffff;      /* white default text color */
        font-family: Consolas, "Courier New", monospace;
        margin: 20px;
    }

    pre {
        white-space: pre;
        font-size: 14px;
        line-height: 1.4em;
    }

    .timestamp {
        color: #00ff00; /* green */
    }

    .prompt, .interactive, .success {
        color: #00ff00; /* green */
    }

    .highlight {
        color: cyan;
    }

    .label {
        color: #ffffff; /* white or slight gray */
    }

    .dim {
        color: #aaaaaa;
    }

    /* For the summary table at the end */
    .table-header, .table-cell {
        color: #ffffff;
    }
    .table-header {
        font-weight: bold;
    }
    .success-text {
        color: #00ff00;
    }

    .table-line {
        color: #ffffff;
    }
</style>
</head>
<body>
<pre>
<span class="timestamp">[11:03:45]</span> Logging initialized. Log file: split_video.log
Interactive Mode:

Enter the path to the input video file (): <span class="dim">C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\output_segments\fwfwe.mp4</span>
Enter the output directory for video segments (<span class="dim">output_segments</span>):
Do you want to provide a timecodes CSV or YAML file? [y/n] (n):

Video Duration: <span class="highlight">05:00</span> | Acceptable Formats: MM:SS, SS, MMSS, SS

Enter timecodes for splitting the video:

Segment 1 name (<span class="label">clip1</span>):
Start time: <span class="label">00:30</span>
End time: <span class="label">01:30</span>
Added segment '<span class="label">clip1</span>': 30 to 01:30

Do you want to add another segment? [y/n] (n):

Input Video: <span class="dim">C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\output_segments\fwfwe.mp4</span> | Output Directory: <span class="dim">output_segments</span> | Number of Segments: <span class="dim">1</span>

Segment '<span class="label">clip1</span>' created successfully.

Splitting Summary

┌───────────────┬─────────────────────────────────────┬──────────┐
│ <span class="table-header">Segment Name</span>  │ <span class="table-header">Output Path</span>                 │ <span class="table-header">Status</span>   │
├───────────────┼─────────────────────────────────────┼──────────┤
│ <span class="table-cell">clip1</span>         │ <span class="table-cell">output_segments\clip1.mp4</span>    │ <span class="success-text">Success</span>  │
└───────────────┴─────────────────────────────────────┴──────────┘
</pre>
</body>
</html>
