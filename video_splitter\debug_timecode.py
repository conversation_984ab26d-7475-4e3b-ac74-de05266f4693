#!/usr/bin/env python3
"""
Debug the timecode parsing issue step by step
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from main import TimecodeParser

def debug_timecode_parsing():
    """Debug the exact parsing flow for 00:17:30."""
    
    print("=== Debugging Timecode Parsing ===")
    test_input = "00:17:30"
    print(f"Input: '{test_input}'")
    
    # Step 1: Test normalize_timecode
    print("\n1. Testing normalize_timecode:")
    try:
        normalized = TimecodeParser.normalize_timecode(test_input)
        print(f"   Normalized: '{normalized}'")
    except Exception as e:
        print(f"   ERROR in normalize_timecode: {e}")
        return
    
    # Step 2: Test parse_timecode
    print("\n2. Testing parse_timecode:")
    try:
        seconds = TimecodeParser.parse_timecode(test_input)
        print(f"   Parsed seconds: {seconds}")
        print(f"   Expected: 17.30")
        print(f"   Match: {'✓' if abs(seconds - 17.30) < 0.01 else '✗'}")
    except Exception as e:
        print(f"   ERROR in parse_timecode: {e}")
        return
    
    # Step 3: Test the actual YAML loading process
    print("\n3. Testing YAML segment processing:")
    test_segment = ("00:00:00", "00:17:30", "test")
    print(f"   Segment: {test_segment}")
    
    try:
        start_seconds = TimecodeParser.parse_timecode(test_segment[0])
        end_seconds = TimecodeParser.parse_timecode(test_segment[1])
        print(f"   Start: {start_seconds} seconds")
        print(f"   End: {end_seconds} seconds")
        print(f"   Duration: {end_seconds - start_seconds} seconds")
        
        if abs(end_seconds - 17.30) < 0.01:
            print("   ✓ End time correctly parsed as 17.30 seconds")
        else:
            print(f"   ✗ End time incorrectly parsed as {end_seconds} seconds")
            
    except Exception as e:
        print(f"   ERROR in segment processing: {e}")

def test_edge_cases():
    """Test various edge cases."""
    
    print("\n=== Testing Edge Cases ===")
    
    test_cases = [
        ("00:17:30", 17.30, "Your specific case"),
        ("00:01:30", 90.0, "Should be 1 minute 30 seconds"),
        ("00:59:99", 59.99, "Edge case: 59.99 seconds"),
        ("01:17:30", 4650.0, "Should be 1 hour 17 minutes 30 seconds"),
        ("17:30", 17.30, "Two-part format"),
        ("17.30", 17.30, "Decimal format"),
    ]
    
    for input_str, expected, description in test_cases:
        try:
            result = TimecodeParser.parse_timecode(input_str)
            status = "✓" if abs(result - expected) < 0.01 else "✗"
            print(f"{status} '{input_str}' -> {result:.2f}s (expected {expected:.2f}s) - {description}")
        except Exception as e:
            print(f"✗ '{input_str}' -> ERROR: {e} - {description}")

if __name__ == "__main__":
    debug_timecode_parsing()
    test_edge_cases()
