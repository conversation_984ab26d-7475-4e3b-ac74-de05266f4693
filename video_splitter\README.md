# Video Splitter

## Overview
Video segmentation tool with FFmpeg integration and rich CLI interface, supporting precise time-based video splitting with YAML/CSV configuration.

## Features
- **Flexible time formats**: Support for HH:MM:SS, MM:SS, and numeric formats
- **YAML/CSV configuration**: Load segments and settings from configuration files
- **Interactive CLI**: User-friendly prompts with Rich-based interface
- **Concurrent processing**: Multi-threaded segment extraction for faster processing
- **Adobe After Effects compatibility**: Re-encode segments for maximum AE compatibility
- **Comprehensive validation**: Overlap detection and duration checking
- **Progress tracking**: Real-time processing progress with visual indicators
- **Flexible output naming**: Timestamp appending and source prefix options

## Quick Start
Run `run.bat` to start the interactive video splitter (handles environment setup automatically)

## Usage
```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py --prompt
uv run python src/main.py -i "video.mp4" -op "output/" -tc "timecodes.yaml"
```

## Configuration File Example
```yaml
# timecodes.yaml
input_video: "path/to/video.mp4"
output_dir: "output/segments"
use_subdirectory: true

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"
output_format: "mp4"
codec: "copy"
threads: 4
after_effects_compatible: false
append_timestamps: true
include_source_prefix: false

segments:
  - start: "00:46"
    end: "01:16"
    title: "Introduction"
  - start: "01:17"
    end: "05:17"
    title: "Main Content"
  - start: "08:21"
    end: "11:12"
    title: "Conclusion"
```

## Requirements
- **FFmpeg**: Required for video processing (must be in PATH or specify path)
- **FFprobe**: Required for video metadata extraction (usually comes with FFmpeg)

## Dependencies
Managed via `pyproject.toml` with uv package manager.

## Command Line Options
- `-i, --input_video`: Path to input video file
- `-op, --output_path`: Output directory path
- `-tc, --timecodes_file`: Path to YAML/CSV timecodes file
- `--ffmpeg_path`: Path to ffmpeg executable (default: "ffmpeg")
- `--ffprobe_path`: Path to ffprobe executable (default: "ffprobe")
- `--output_format`: Output video format (default: "mp4")
- `--codec`: Video codec (default: "copy")
- `--threads`: Number of concurrent threads (default: 1)
- `--after_effects_compatible`: Re-encode for Adobe After Effects compatibility
- `--append_timestamps`: Append timestamps to output filenames
- `--include_source_prefix`: Include source filename as prefix
- `--use_subdirectory`: Create subdirectory for output files
- `--prompt`: Interactive mode
