<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<title>Terminal Output Prototype</title>
<style>
    /* ===========================
       CSS VARIABLES & THEME
       Modify these for quick color/theme changes
       =========================== */
    :root {
        --color-bg: #000000;                /* Main background */
        --color-fg: #ffffff;                /* Default foreground text */
        --color-timestamp: #00ff00;         /* Timestamp or logging messages */
        --color-prompt: #00ffff;            /* User prompts/questions */
        --color-path: #ffcc66;              /* File paths, directories */
        --color-metadata: #bbbbbb;          /* Additional info, metadata text */
        --color-success: #00ff00;           /* Success messages */
        --color-error: #ff3333;             /* Errors or critical messages */
        --color-highlight: #ffff00;         /* Highlight important items */
        --color-table-border: #ffffff;      /* Table border color */

        --font-family: Consolas, "Courier New", monospace;
        --font-size: 14px;
        --line-height: 1.4em;

        /* Spacing adjustments */
        --spacing-section: 1em;
        --spacing-line: 0.5em;
    }

    /* ===========================
       GLOBAL STYLES
       =========================== */
    body {
        background: var(--color-bg);
        color: var(--color-fg);
        font-family: var(--font-family);
        font-size: var(--font-size);
        line-height: var(--line-height);
        margin: 20px;
    }

    pre {
        white-space: pre;
        margin: 0;
    }

    /* Utility classes for spacing */
    .section {
        margin-bottom: var(--spacing-section);
    }

    .line {
        margin-bottom: var(--spacing-line);
    }

    /* ===========================
       COLOR & CATEGORY CLASSES
       =========================== */
    .timestamp {
        color: var(--color-timestamp);
    }

    .prompt {
        color: var(--color-prompt);
        font-weight: bold;
    }

    .path {
        color: var(--color-path);
    }

    .metadata {
        color: var(--color-metadata);
    }

    .success {
        color: var(--color-success);
        font-weight: bold;
    }

    .error {
        color: var(--color-error);
        font-weight: bold;
    }

    .highlight {
        color: var(--color-highlight);
        font-weight: bold;
    }

    /* For visually separating sections or groups */
    .divider {
        border-top: 1px solid var(--color-table-border);
        margin: var(--spacing-line) 0;
    }

    /* ===========================
       TABLE STYLES
       =========================== */
    .summary-table {
        margin-top: var(--spacing-line);
        border-collapse: collapse;
    }

    .summary-table th,
    .summary-table td {
        border: 1px solid var(--color-table-border);
        padding: 0.5em;
    }

    .summary-table th {
        text-align: left;
        color: var(--color-highlight);
    }

    /* Example of success/failure styling in table cells */
    .status-success {
        color: var(--color-success);
        font-weight: bold;
    }

    .status-failed {
        color: var(--color-error);
        font-weight: bold;
    }

    /* Adjusting prompts or user input lines */
    .user-input-section {
        margin-top: var(--spacing-line);
        margin-bottom: var(--spacing-line);
    }

    /* This class can wrap a line of text that represents a command or action taken */
    .command-line {
        color: var(--color-metadata);
        font-style: italic;
    }

</style>
</head>
<body>
<pre>

<!-- LOGGING/INITIALIZATION SECTION -->
<div class="section">
    <span class="timestamp">[11:03:45]</span> <span class="metadata">Logging initialized.</span> Log file: <span class="metadata">split_video.log</span>
</div>

<!-- INTERACTIVE MODE PROMPT SECTION -->
<div class="section">
    <span class="prompt">Interactive Mode:</span>

    <div class="user-input-section">
        <span class="prompt">Enter the path to the input video file</span> (): <span class="path">C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\output_segments\fwfwe.mp4</span>
    </div>
    <div class="user-input-section">
        <span class="prompt">Enter the output directory for video segments</span> (<span class="metadata">output_segments</span>):
    </div>
    <div class="user-input-section">
        <span class="prompt">Do you want to provide a timecodes CSV or YAML file?</span> [y/n] (n):
    </div>
</div>

<!-- METADATA DISPLAY SECTION -->
<div class="section">
    <span class="metadata">Video Duration:</span> <span class="highlight">05:00</span> <span class="metadata">| Acceptable Formats: MM:SS, SS, MMSS, SS</span>

    <div class="user-input-section">
        <span class="prompt">Enter timecodes for splitting the video:</span>
    </div>

    <div class="line">
        Segment 1 name (<span class="metadata">clip1</span>):
    </div>
    <div class="line">
        Start time: <span class="metadata">00:30</span>
    </div>
    <div class="line">
        End time: <span class="metadata">01:30</span>
    </div>
    <div class="line">
        <span class="success">Added segment 'clip1': 30 to 01:30</span>
    </div>

    <div class="user-input-section">
        <span class="prompt">Do you want to add another segment?</span> [y/n] (n):
    </div>

    <div class="line divider"></div>

    <div class="line">
        <span class="metadata">Input Video:</span> <span class="path">C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\output_segments\fwfwe.mp4</span>
        <span class="metadata">| Output Directory:</span> <span class="path">output_segments</span>
        <span class="metadata">| Number of Segments:</span> <span class="metadata">1</span>
    </div>

    <div class="line">
        <span class="success">Segment 'clip1' created successfully.</span>
    </div>
</div>

<!-- SUMMARY TABLE SECTION -->
<div class="section">
    <span class="highlight">Splitting Summary</span>
    <table class="summary-table">
        <thead>
            <tr>
                <th>Segment Name</th>
                <th>Output Path</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>clip1</td>
                <td><span class="path">output_segments\clip1.mp4</span></td>
                <td class="status-success">Success</td>
            </tr>
        </tbody>
    </table>
</div>

</pre>
</body>
</html>
