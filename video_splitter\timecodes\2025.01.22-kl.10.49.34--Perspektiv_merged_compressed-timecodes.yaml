# input-timecodes.yaml

# 2025.01.22-kl.10.49.34--Perspektiv_merged_compressed

# input_video: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/_me/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
# use_subdirectory: true
# output_dir: "C:/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv"

input_video: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
use_subdirectory: true
output_dir: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter"

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"
output_format: "mp4"
codec: "copy"
threads: 4
after_effects_compatible: false
append_timestamps: true
include_source_prefix: false

segments:
  - start: "00:06"
    end: "00:17"
    title: "a-starte videoopptak"

  # - start: "00:34"
  #   end: "04:44"
  #   title: "b-dele perspektiv"

  # - start: "04:54:00"
  #   end: "06:43:20"
  #   title: "c-det uperfekte, hånd i hånd"
  #   # title: "c-vi står hånd i hånd"

  - start: "07:01:16"
    end: "09:39:12"
    title: "d-oppmerksomheten vår"


# log_file: "split_video.log"
# verbose: true
# prompt: false
