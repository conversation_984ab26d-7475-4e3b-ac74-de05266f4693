import argparse
import os
import subprocess
import sys
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from dataclasses import dataclass
from decimal import Decimal, ROUND_DOWN
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import re

import yaml
from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.progress import (Progress, SpinnerColumn, TextColumn,
                          BarColumn, TimeElapsedColumn)
from rich.prompt import Prompt, Confirm
from rich.table import Table

console = Console()


class TimecodeParser:
    """Strict-but-friendly parser:
       - HH:MM:SS[.mmm]  => milliseconds
       - HH:MM:SS:FF     => frames (requires fps)
       - Optional: treat HH:MM:SS.FF as frames when decimal_as_frames=True
    """

    @staticmethod
    def parse_timecode(
        time_str: str,
        fps: Optional[int] = None,
        decimal_as_frames: bool = False
    ) -> float:
        """Return total seconds with ms precision.

        Accepted forms:
        - HH:MM:SS
        - HH:MM:SS.m[mm]        (milliseconds unless decimal_as_frames=True and looks like frames)
        - HH:MM:SS:FF           (frames; requires fps; FF < fps)
        """
        time_str = time_str.strip()

        # 1) Frames with colon: HH:MM:SS:FF
        m_frames = re.match(r'^(\d{1,2}):(\d{2}):(\d{2}):(\d{2})$', time_str)
        if m_frames:
            if fps is None:
                raise ValueError("Timecode uses frames (HH:MM:SS:FF) but fps was not provided.")
            hours = int(m_frames.group(1))
            minutes = int(m_frames.group(2))
            seconds = int(m_frames.group(3))
            frames = int(m_frames.group(4))

            if minutes >= 60:
                raise ValueError(f"Invalid minutes: {minutes} (must be < 60)")
            if seconds >= 60:
                raise ValueError(f"Invalid seconds: {seconds} (must be < 60)")
            if frames >= fps:
                raise ValueError(f"Invalid frames: {frames} (must be < fps={fps})")

            return hours * 3600 + minutes * 60 + seconds + frames / float(fps)

        # 2) Milliseconds or ".FF as frames": HH:MM:SS[.mmm]
        m_ms = re.match(r'^(\d{1,2}):(\d{2}):(\d{2})(?:\.(\d{1,3}))?$', time_str)
        if m_ms:
            hours = int(m_ms.group(1))
            minutes = int(m_ms.group(2))
            seconds = int(m_ms.group(3))
            frac = m_ms.group(4)

            if minutes >= 60:
                raise ValueError(f"Invalid minutes: {minutes} (must be < 60)")
            if seconds >= 60:
                raise ValueError(f"Invalid seconds: {seconds} (must be < 60)")

            if frac is None:
                return hours * 3600 + minutes * 60 + seconds

            # If requested, treat short decimals as frames when plausible
            if decimal_as_frames and fps:
                if 1 <= len(frac) <= 2:
                    ff = int(frac)
                    if ff < fps:
                        return hours * 3600 + minutes * 60 + seconds + ff / float(fps)

            # Otherwise: normalize milliseconds (.5 -> .500, .05 -> .050)
            milliseconds = int(frac.ljust(3, '0'))
            if milliseconds >= 1000:
                raise ValueError(f"Invalid milliseconds: {milliseconds} (must be < 1000)")
            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0

        raise ValueError(f"Invalid timecode: '{time_str}'. Use HH:MM:SS[.mmm] or HH:MM:SS:FF.")

    @staticmethod
    def format_seconds(seconds: float) -> str:
        """Format seconds as HH:MM:SS.mmm with millisecond precision, no rounding up."""
        d = Decimal(str(seconds)).quantize(Decimal('0.001'), rounding=ROUND_DOWN)
        total_ms = int(d * 1000)

        hours, rem_ms = divmod(total_ms, 3600_000)
        minutes, rem_ms = divmod(rem_ms, 60_000)
        secs, milliseconds = divmod(rem_ms, 1000)

        if milliseconds > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"
        else:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    @staticmethod
    def format_duration(seconds: float) -> str:
        return TimecodeParser.format_seconds(seconds)


@dataclass
class VideoSegment:
    name: str
    start: str
    end: str


def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Split a video into segments using FFmpeg"
    )
    parser.add_argument("-i", "--input_video", type=str, help="Path to input video file")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
    parser.add_argument("-tc", "--timecodes_file", type=str, help="Path to YAML/CSV timecodes file")
    parser.add_argument("--ffmpeg_path", default="ffmpeg", help="Path to ffmpeg executable")
    parser.add_argument("--ffprobe_path", default="ffprobe", help="Path to ffprobe executable")
    parser.add_argument("--output_format", default="mp4", help="Output video format")
    parser.add_argument("--codec_profile", default="h264_intra",
                        choices=["fast_copy", "h264_intra", "prores_hq", "legacy_ae"],
                        help="Encoding profile")
    parser.add_argument("--quality", type=int, default=18, choices=range(0, 52),
                        help="CRF quality (0-51)")
    parser.add_argument("--threads", type=int, default=1, help="Number of concurrent threads")
    parser.add_argument("--append_timestamps", action="store_true", default=True,
                        help="Append timestamps to output filenames")
    parser.add_argument("--include_source_prefix", action="store_true", default=False,
                        help="Include source filename as prefix")
    parser.add_argument("--use_subdirectory", action="store_true", default=False,
                        help="Create subdirectory for output files")
    parser.add_argument("--prompt", action="store_true", help="Interactive mode")
    parser.add_argument("--fps", type=int, default=None,
                        help="Frames per second. Used for HH:MM:SS:FF and optionally HH:MM:SS.FF.")
    parser.add_argument("--decimal-frames", choices=["auto", "on", "off"], default="auto",
                        help="Interpret HH:MM:SS.FF as frames (FF) instead of fractional seconds. Default: auto.")
    return parser.parse_args()


def setup_logger():
    logger.remove()
    logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")


def wait_for_user_exit():
    console.print("\n[dim]Press Enter to exit...[/dim]")
    input()


def get_video_duration(video_path: str, ffprobe_path: str = "ffprobe") -> float:
    cmd = [
        ffprobe_path, "-v", "error", "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1", video_path
    ]
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return float(result.stdout.decode().strip())
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Failed to get video duration: {e.stderr.decode().strip()}")


def get_video_fps(video_path: str, ffprobe_path: str = "ffprobe") -> Optional[int]:
    """Detect fps and snap 23.976/29.97/59.94 to 24/30/60."""
    cmd = [
        ffprobe_path, "-v", "error",
        "-select_streams", "v:0",
        "-show_entries", "stream=avg_frame_rate",
        "-of", "default=noprint_wrappers=1:nokey=1",
        video_path
    ]
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        val = result.stdout.decode().strip()
        if "/" in val:
            num, den = val.split("/", 1)
            den = float(den)
            if den == 0:
                return None
            fps_float = float(num) / den
        else:
            fps_float = float(val)
        common = [24, 25, 30, 50, 60]
        nearest = min(common, key=lambda c: abs(c - fps_float))
        if abs(nearest - fps_float) < 0.6:
            return nearest
        return int(round(fps_float))
    except Exception:
        return None


def load_timecodes_from_yaml(file_path: str) -> Dict[str, Any]:
    with open(file_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f) or {}

    segments = []
    for seg in data.get('segments', []):
        if seg.get('start') and seg.get('end') and (seg.get('title') or seg.get('name')):
            segments.append((
                seg['start'].strip(),
                seg['end'].strip(),
                (seg.get('title') or seg.get('name')).strip()
            ))

    # Allow decimal_frames in YAML as true/false/auto
    yaml_decimal_frames = data.get('decimal_frames', 'auto')
    if isinstance(yaml_decimal_frames, bool):
        yaml_decimal_frames = 'on' if yaml_decimal_frames else 'off'
    else:
        yaml_decimal_frames = str(yaml_decimal_frames).strip().lower()
        if yaml_decimal_frames not in ('auto', 'on', 'off', 'true', 'false', 'yes', 'no'):
            yaml_decimal_frames = 'auto'
        if yaml_decimal_frames in ('true', 'yes'):
            yaml_decimal_frames = 'on'
        if yaml_decimal_frames in ('false', 'no'):
            yaml_decimal_frames = 'off'

    return {
        'input_video': data.get('input_video'),
        'output_dir': data.get('output_dir'),
        'segments': segments,
        'ffmpeg_path': data.get('ffmpeg_path', 'ffmpeg'),
        'ffprobe_path': data.get('ffprobe_path', 'ffprobe'),
        'output_format': data.get('output_format', 'mp4'),
        'codec_profile': data.get('codec_profile', 'h264_intra'),
        'quality': data.get('quality', 18),
        'threads': data.get('threads', 1),
        'append_timestamps': data.get('append_timestamps', True),
        'include_source_prefix': data.get('include_source_prefix', False),
        'use_subdirectory': data.get('use_subdirectory', False),
        'fps': data.get('fps'),
        'decimal_frames': yaml_decimal_frames
    }


def _has_colon_frames(segs: List[Tuple[str, str, str]]) -> bool:
    pat = re.compile(r'^\d{1,2}:\d{2}:\d{2}:\d{2}$')
    for s, e, _ in segs:
        if pat.match(s) or pat.match(e):
            return True
    return False


def _decimals_look_like_frames(segs: List[Tuple[str, str, str]], fps: Optional[int]) -> bool:
    """Heuristic: if all decimal fractions are 1–2 digits, all < fps,
    and there is no 3-digit .mmm anywhere → assume they're frames."""
    if not fps:
        return False
    any_fraction = False
    has_three = False
    values: List[int] = []
    pat = re.compile(r'^\d{1,2}:\d{2}:\d{2}\.(\d{1,3})$')
    for s, e, _ in segs:
        for t in (s, e):
            m = pat.match(t)
            if not m:
                continue
            any_fraction = True
            frac = m.group(1)
            if len(frac) == 3:
                has_three = True
            elif len(frac) in (1, 2):
                try:
                    values.append(int(frac))
                except Exception:
                    return False
    if has_three:
        return False
    if not any_fraction:
        return False
    if not values:
        return False
    return all(0 <= v < fps for v in values)


def _resolve_decimal_frames_mode(cli_mode: str, yaml_mode: str) -> str:
    """CLI overrides YAML. Normalize to 'auto'|'on'|'off'."""
    pref = cli_mode or 'auto'
    if pref not in ('auto', 'on', 'off'):
        pref = 'auto'
    y = yaml_mode or 'auto'
    if y not in ('auto', 'on', 'off'):
        y = 'auto'
    return pref if pref != 'auto' else y


def _normalize_segments(
    segs: List[Tuple[str, str, str]],
    fps: Optional[int],
    decimal_as_frames: bool
) -> List[Tuple[str, str, str]]:
    """Parse and reformat all segments to canonical HH:MM:SS.mmm strings."""
    normalized: List[Tuple[str, str, str]] = []
    for s, e, name in segs:
        s_sec = TimecodeParser.parse_timecode(s, fps=fps, decimal_as_frames=decimal_as_frames)
        e_sec = TimecodeParser.parse_timecode(e, fps=fps, decimal_as_frames=decimal_as_frames)
        if e_sec <= s_sec:
            raise ValueError(f"Invalid segment '{name}': end <= start ({s} -> {e})")
        s_fmt = TimecodeParser.format_seconds(s_sec)
        e_fmt = TimecodeParser.format_seconds(e_sec)
        normalized.append((s_fmt, e_fmt, name))
    return normalized


def prompt_for_segments(video_duration: float, fps: Optional[int], decimal_as_frames: bool) -> List[Tuple[str, str, str]]:
    formatted_dur = TimecodeParser.format_duration(video_duration)
    fmts = "HH:MM:SS.mmm (milliseconds) or HH:MM:SS:FF (frames)"
    console.print(f"\n[bold cyan]Video Duration:[/bold cyan] [dim]{formatted_dur}[/dim] | "
                  f"[bold cyan]Format:[/bold cyan] [dim]{fmts}[/dim]\n")
    if fps:
        mode = "frames" if decimal_as_frames else "milliseconds"
        console.print(f"[bold cyan]FPS:[/bold cyan] [dim]{fps}[/dim]  "
                      f"[bold cyan]'.xx' decimals interpreted as:[/bold cyan] [dim]{mode}[/dim]\n")

    result: List[Tuple[str, str, str]] = []
    idx = 1

    while True:
        name = Prompt.ask(f"[bold cyan]Segment {idx} name[/bold cyan]", default=f"clip{idx}")

        start_sec = None
        while start_sec is None:
            st = Prompt.ask("[bold cyan]Start time (HH:MM:SS.mmm or HH:MM:SS:FF)[/bold cyan]")
            try:
                start_sec = TimecodeParser.parse_timecode(st, fps=fps, decimal_as_frames=decimal_as_frames)
            except ValueError as v:
                console.print(f"[bold red]{v}[/bold red]")

        end_sec = None
        while end_sec is None:
            en = Prompt.ask("[bold cyan]End time (HH:MM:SS.mmm or HH:MM:SS:FF)[/bold cyan]")
            try:
                en_sec = TimecodeParser.parse_timecode(en, fps=fps, decimal_as_frames=decimal_as_frames)
                if en_sec <= start_sec:
                    raise ValueError("End must be greater than start.")
                if en_sec > video_duration:
                    raise ValueError("End exceeds video duration.")
                end_sec = en_sec
            except ValueError as v:
                console.print(f"[bold red]{v}[/bold red]")

        st_form = TimecodeParser.format_seconds(start_sec)
        en_form = TimecodeParser.format_seconds(end_sec)
        result.append((st_form, en_form, name))
        console.print(f"[bold green]Added '{name}': {st_form} to {en_form}[/bold green]\n")
        idx += 1

        if not Confirm.ask("[bold cyan]Add another segment?[/bold cyan]", default=False):
            break

    return result


def discover_and_select_timecode_file() -> Optional[str]:
    timecodes_dir = Path("timecodes")
    if not timecodes_dir.exists():
        console.print(f"[bold yellow]Timecodes directory '{timecodes_dir}' not found[/bold yellow]")
        return None
    yaml_files = list(timecodes_dir.glob("*.yaml")) + list(timecodes_dir.glob("*.yml"))
    if not yaml_files:
        console.print(f"[bold yellow]No YAML timecode files found in '{timecodes_dir}'[/bold yellow]")
        return None

    console.print(f"\n[bold cyan]Available timecode files in '{timecodes_dir}':[/bold cyan]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("No.", style="cyan", width=4)
    table.add_column("Filename", style="green")
    table.add_column("Size", style="dim", width=8)

    for idx, file_path in enumerate(yaml_files, 1):
        file_size = f"{file_path.stat().st_size}B" if file_path.exists() else "N/A"
        table.add_row(str(idx), file_path.name, file_size)

    console.print(table)

    while True:
        try:
            choice = Prompt.ask(
                f"[bold cyan]Select timecode file (1-{len(yaml_files)}) or 'q' to skip[/bold cyan]",
                default="q"
            )
            if choice.lower() == 'q':
                return None
            file_idx = int(choice) - 1
            if 0 <= file_idx < len(yaml_files):
                selected_file = str(yaml_files[file_idx])
                console.print(f"[bold green]Selected: {selected_file}[/bold green]")
                return selected_file
            else:
                console.print(f"[bold red]Invalid selection. Choose 1-{len(yaml_files)} or 'q'[/bold red]")
        except ValueError:
            console.print(f"[bold red]Invalid input. Enter a number (1-{len(yaml_files)}) or 'q'[/bold red]")


def check_output_directory(output_dir: str):
    if not Path(output_dir).exists():
        if Confirm.ask(f"[bold cyan]Create output directory '{output_dir}'?[/bold cyan]", default=True):
            try:
                os.makedirs(output_dir, exist_ok=True)
                console.print(f"[bold green]Created directory: {output_dir}[/bold green]\n")
            except Exception as e:
                console.print(f"[bold red]Error creating directory: {e}[/bold red]")
                sys.exit(1)
        else:
            console.print("[yellow]Operation cancelled by user[/yellow]")
            sys.exit(1)


def get_user_inputs(args):
    yaml_config: Dict[str, Any] = {}

    # Load YAML if desired
    if args.prompt and not args.timecodes_file:
        if Confirm.ask("[bold cyan]Load a YAML/CSV timecodes file?[/bold cyan]", default=True):
            while True:
                timecodes_path = Prompt.ask("[bold cyan]Path to timecodes file (or press Enter to browse)[/bold cyan]", default="")
                if not timecodes_path.strip():
                    selected_file = discover_and_select_timecode_file()
                    if selected_file:
                        args.timecodes_file = selected_file
                    break
                if Path(timecodes_path).is_file():
                    args.timecodes_file = timecodes_path
                    break
                else:
                    console.print(f"[bold red]File not found: {timecodes_path}[/bold red]")

    if args.timecodes_file and Path(args.timecodes_file).is_file():
        try:
            yaml_config = load_timecodes_from_yaml(args.timecodes_file)
            console.print(f"[bold green]Loaded configuration from {args.timecodes_file}[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error loading YAML file: {e}[/bold red]")

    # Input video
    if args.prompt or not args.input_video:
        default_video = yaml_config.get('input_video') or args.input_video or ""
        args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]", default=default_video)
        while not Path(args.input_video).is_file():
            console.print("[bold red]File not found. Try again.[/bold red]")
            args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]")
    if not args.input_video or not Path(args.input_video).is_file():
        console.print("[bold red]Error: Input video file not found[/bold red]")
        sys.exit(1)

    # Early FPS auto-detect if not provided
    if getattr(args, "fps", None) in (None, 0):
        detected = get_video_fps(args.input_video, yaml_config.get('ffprobe_path', args.ffprobe_path))
        if detected:
            if args.prompt:
                if Confirm.ask(f"[bold cyan]Detected FPS {detected}. Use it?[/bold cyan]", default=True):
                    args.fps = detected
            else:
                args.fps = detected

    # Interactive options
    if args.prompt:
        use_subdir = Confirm.ask("[bold cyan]Place output in a separate subdirectory?[/bold cyan]",
                                 default=yaml_config.get("use_subdirectory", False))
        if use_subdir:
            args.output_path = Prompt.ask("[bold cyan]Output directory[/bold cyan]",
                                          default=yaml_config.get("output_dir", "output_segments"))
        else:
            args.output_path = str(Path(args.input_video).parent)

        args.append_timestamps = Confirm.ask("[bold cyan]Append timestamps to filenames?[/bold cyan]",
                                             default=yaml_config.get("append_timestamps", True))
        args.include_source_prefix = Confirm.ask("[bold cyan]Include source file's name as prefix?[/bold cyan]",
                                                 default=yaml_config.get("include_source_prefix", False))

        console.print("\n[bold cyan]Encoding Profile Options:[/bold cyan]")
        console.print("  [dim]fast_copy[/dim]    - Stream copy (fastest, may have seeking issues)")
        console.print("  [dim]h264_intra[/dim]   - All-Intra H.264 (frame-accurate, editing-optimized)")
        console.print("  [dim]prores_hq[/dim]    - ProRes 422 HQ (professional mezzanine)")
        console.print("  [dim]legacy_ae[/dim]    - Legacy After Effects compatibility")

        default_profile = yaml_config.get("codec_profile", "h264_intra")
        args.codec_profile = Prompt.ask("[bold cyan]Select encoding profile[/bold cyan]",
                                        choices=["fast_copy", "h264_intra", "prores_hq", "legacy_ae"],
                                        default=default_profile)

        if args.codec_profile != "fast_copy":
            console.print("\n[bold cyan]Quality Settings (CRF):[/bold cyan]")
            console.print("  [dim]0[/dim]     - Lossless (largest files)")
            console.print("  [dim]18[/dim]    - Visually lossless (default)")
            console.print("  [dim]23[/dim]    - Standard quality")
            console.print("  [dim]28[/dim]    - Smaller files, good quality")
            console.print("  [dim]35[/dim]    - Much smaller files, noticeable quality loss")

            default_quality = yaml_config.get("quality", 18)
            args.quality = Prompt.ask("[bold cyan]Select quality setting (0-51)[/bold cyan]",
                                      default=str(default_quality))
            try:
                args.quality = int(args.quality)
                if not 0 <= args.quality <= 51:
                    raise ValueError("Quality must be between 0 and 51")
            except ValueError:
                console.print("[bold red]Invalid quality setting. Using default (18)[/bold red]")
                args.quality = 18
    elif not args.output_path:
        default_output = yaml_config.get('output_dir') or args.output_path
        if not default_output:
            default_output = "output_segments" if yaml_config.get('use_subdirectory', False) else str(Path(args.input_video).parent)
        args.output_path = default_output

    # Segments (load or prompt)
    segments = yaml_config.get('segments', [])
    if args.prompt or not segments:
        if segments:
            console.print(f"[bold green]Found {len(segments)} segments from YAML/CSV.[/bold green]")
            if args.prompt:
                use_existing = Confirm.ask("[bold cyan]Use these segments as-is?[/bold cyan]", default=True)
                if not use_existing:
                    discard = Confirm.ask("[bold cyan]Discard loaded segments and define new ones?[/bold cyan]", default=True)
                    if discard:
                        segments = []
        if not segments:
            try:
                duration = get_video_duration(args.input_video, yaml_config.get('ffprobe_path', args.ffprobe_path))
                fps_for_prompt = yaml_config.get('fps', getattr(args, 'fps', None))
                # Decide decimal-as-frames mode for prompt (use YAML/CLI mode if present)
                mode = _resolve_decimal_frames_mode(getattr(args, "decimal_frames", "auto"),
                                                    yaml_config.get("decimal_frames", "auto"))
                auto_frames = _decimals_look_like_frames([], fps_for_prompt)  # none yet, so False
                decimal_as_frames_prompt = (mode == 'on') or (mode == 'auto' and auto_frames)
                segments = prompt_for_segments(duration, fps=fps_for_prompt, decimal_as_frames=decimal_as_frames_prompt)
            except Exception as e:
                console.print(f"[bold red]Error getting video duration: {e}[/bold red]")
                sys.exit(1)

    # Merge YAML config with CLI (CLI takes precedence)
    for key, value in yaml_config.items():
        if not hasattr(args, key) or getattr(args, key) is None:
            setattr(args, key, value)

    # Resolve decimal-frames mode (CLI overrides YAML)
    args.decimal_frames = _resolve_decimal_frames_mode(getattr(args, "decimal_frames", "auto"),
                                                       yaml_config.get("decimal_frames", "auto"))

    # Final decision: should decimals be treated as frames?
    uses_colon_frames = _has_colon_frames(segments)
    auto_guess = _decimals_look_like_frames(segments, getattr(args, "fps", None)) or uses_colon_frames
    decimal_as_frames_effective = (args.decimal_frames == 'on') or (args.decimal_frames == 'auto' and auto_guess)

    # If any HH:MM:SS:FF present and fps missing, try to detect or ask
    if uses_colon_frames and getattr(args, "fps", None) in (None, 0):
        detected_fps = get_video_fps(args.input_video, getattr(args, "ffprobe_path", "ffprobe"))
        if args.prompt:
            if detected_fps:
                if Confirm.ask(f"[bold cyan]Segments include frame timecodes. Use detected FPS {detected_fps}?[/bold cyan]", default=True):
                    args.fps = detected_fps
            else:
                while True:
                    val = Prompt.ask("[bold cyan]Enter FPS for HH:MM:SS:FF timecodes[/bold cyan]")
                    try:
                        args.fps = int(val)
                        if args.fps <= 0:
                            raise ValueError
                        break
                    except Exception:
                        console.print("[bold red]Invalid FPS. Enter a positive integer.[/bold red]")
        else:
            if detected_fps:
                args.fps = detected_fps
            else:
                raise ValueError("Segments use HH:MM:SS:FF but fps is not set and could not be detected. Provide fps in YAML (fps: 25) or CLI (--fps 25).")

    # Normalize segments to canonical .mmm strings using the effective rules
    try:
        segments = _normalize_segments(segments, fps=getattr(args, "fps", None), decimal_as_frames=decimal_as_frames_effective)
    except Exception as e:
        console.print(f"[bold red]Error normalizing segments:[/bold red] {e}")
        sys.exit(1)

    # Persist the effective flags for display
    args.decimal_frames_effective = decimal_as_frames_effective

    return args.input_video, args.output_path, segments, args


def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str,
                       ffmpeg_path: str, output_format: str, codec_profile: str,
                       append_timestamps: bool, include_source_prefix: bool, quality: int = 18) -> Tuple[str, Optional[str], Optional[str]]:
    source_name = Path(input_video).stem if include_source_prefix else ""
    prefix = f"{source_name}_" if include_source_prefix else ""

    if codec_profile == "prores_hq":
        output_format = "mov"

    if append_timestamps:
        start_clean = segment.start.replace(':', '-').replace('.', '_')
        end_clean = segment.end.replace(':', '-').replace('.', '_')
        filename = f"{prefix}{segment.name}_{start_clean}_to_{end_clean}.{output_format}"
    else:
        filename = f"{prefix}{segment.name}.{output_format}"

    output_path = Path(output_dir) / filename

    # Use normalized, canonical strings for exact ffmpeg interpretation
    cmd = [ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end]

    if codec_profile == "fast_copy":
        cmd.extend(["-c", "copy", "-avoid_negative_ts", "make_zero", "-y", str(output_path)])
    elif codec_profile == "h264_intra":
        cmd.extend([
            "-c:v", "libx264", "-preset", "medium", "-crf", str(quality), "-g", "1",
            "-bf", "0", "-pix_fmt", "yuv420p", "-color_primaries", "bt709", "-color_trc", "bt709",
            "-colorspace", "bt709", "-color_range", "tv",
            "-c:a", "pcm_s24le", "-ar", "48000",
            "-vsync", "cfr",
            "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
            "-y", str(output_path)
        ])
    elif codec_profile == "prores_hq":
        cmd.extend([
            "-c:v", "prores_ks", "-profile:v", "3",
            "-pix_fmt", "yuv422p10le",
            "-vendor", "apl0",
            "-bits_per_mb", "8000",
            "-g", "30", "-bf", "0",
            "-colorspace", "bt709", "-color_primaries", "bt709", "-color_trc", "bt709",
            "-c:a", "pcm_s24le", "-ar", "48000",
            "-vsync", "cfr",
            "-avoid_negative_ts", "make_zero",
            "-y", str(output_path)
        ])
    elif codec_profile == "legacy_ae":
        cmd.extend([
            "-c:v", "libx264", "-preset", "faster", "-pix_fmt", "yuv420p",
            "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart",
            "-y", str(output_path)
        ])
    else:
        cmd.extend([
            "-c:v", "libx264", "-preset", "medium", "-crf", str(quality), "-g", "1",
            "-bf", "0", "-pix_fmt", "yuv420p", "-colorspace", "bt709",
            "-color_primaries", "bt709", "-color_trc", "bt709", "-color_range", "tv",
            "-c:a", "pcm_s24le", "-ar", "48000",
            "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
            "-y", str(output_path)
        ])

    try:
        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return segment.name, str(output_path), None
    except subprocess.CalledProcessError as e:
        return segment.name, None, e.stderr.decode().strip()


def process_video_segments(input_video: str, output_dir: str,
                           segments: List[Tuple[str, str, str]],
                           config: argparse.Namespace) -> List[Tuple[str, str]]:
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    segment_objects = [VideoSegment(name, start, end) for start, end, name in segments]
    results: List[Tuple[str, str]] = []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console,
        transient=True
    ) as progress:
        task = progress.add_task("[bold cyan]Processing video segments...[/bold cyan]",
                                 total=len(segment_objects))

        with ThreadPoolExecutor(max_workers=config.threads) as executor:
            future_to_segment = {
                executor.submit(
                    split_video_segment,
                    input_video,
                    segment,
                    output_dir,
                    config.ffmpeg_path,
                    config.output_format,
                    getattr(config, 'codec_profile', 'h264_intra'),
                    config.append_timestamps,
                    config.include_source_prefix,
                    config.quality
                ): segment.name for segment in segment_objects
            }

            for future in as_completed(future_to_segment):
                segment_name = future_to_segment[future]
                try:
                    name, output_path, error = future.result()
                    if output_path:
                        results.append((name, output_path))
                        console.print(f"[bold green]✓ Completed: {name}[/bold green]")
                    else:
                        results.append((name, f"Failed: {error}"))
                        console.print(f"[bold red]✗ Failed: {name}[/bold red]")
                except Exception as e:
                    error_msg = f"Exception: {str(e)}"
                    results.append((segment_name, error_msg))
                    console.print(f"[bold red]✗ Exception: {segment_name}[/bold red]")
                finally:
                    progress.advance(task)

    return results


def display_summary(input_video: str, output_dir: str,
                    segments: List[Tuple[str, str, str]],
                    config: argparse.Namespace):
    table = Table(title="Video Splitting Configuration", show_header=False, box=None)
    table.add_column("Parameter", style="bold cyan")
    table.add_column("Value", style="dim")

    table.add_row("Input Video", input_video)
    table.add_row("Output Directory", output_dir)
    table.add_row("Output Format", config.output_format)
    table.add_row("Codec Profile", getattr(config, 'codec_profile', 'h264_intra'))
    table.add_row("Threads", str(config.threads))
    table.add_row("Append Timestamps", "Yes" if config.append_timestamps else "No")
    table.add_row("Include Source Prefix", "Yes" if config.include_source_prefix else "No")
    table.add_row("FPS (frames mode)", str(getattr(config, "fps", "—")))
    mode_label = getattr(config, "decimal_frames", "auto")
    eff = "Yes" if getattr(config, "decimal_frames_effective", False) else "No"
    table.add_row("'.xx' decimals treated as frames", f"{eff} (mode: {mode_label})")
    table.add_row("Segments", str(len(segments)))
    table.add_row("Quality Setting", str(config.quality))

    console.print("\n[bold blue]--- Operation Summary ---[/bold blue]")
    console.print(Panel(table, border_style="blue"))

    if segments:
        console.print("\n[bold cyan]Timecode Interpretation:[/bold cyan]")
        seg_table = Table(show_header=True, header_style="bold magenta")
        seg_table.add_column("Name", style="cyan")
        seg_table.add_column("Start", style="green")
        seg_table.add_column("End", style="green")
        seg_table.add_column("Interpreted As", style="yellow")

        for start, end, name in segments:
            try:
                start_sec = TimecodeParser.parse_timecode(start, fps=getattr(config, "fps", None))
                end_sec = TimecodeParser.parse_timecode(end, fps=getattr(config, "fps", None))
                duration = end_sec - start_sec
                interpretation = f"{start_sec:.3f}s → {end_sec:.3f}s ({duration:.3f}s)"
                seg_table.add_row(name, start, end, interpretation)
            except Exception as e:
                seg_table.add_row(name, start, end, f"ERROR: {e}")
        console.print(seg_table)


def display_results(results: List[Tuple[str, str]]):
    table = Table(title="Processing Results", show_header=True, header_style="bold cyan")
    table.add_column("Segment", style="cyan")
    table.add_column("Status", style="dim")
    table.add_column("Output", style="dim")

    for name, result in results:
        if result.startswith("Failed") or result.startswith("Exception"):
            table.add_row(name, "[bold red]Failed[/bold red]", result)
        else:
            table.add_row(name, "[bold green]Success[/bold green]", result)

    console.print("\n")
    console.print(table)


def main():
    args = parse_arguments()
    input_video, output_path, segments, config = get_user_inputs(args)

    setup_logger()

    try:
        check_output_directory(output_path)
        display_summary(input_video, output_path, segments, config)

        if not Confirm.ask("\n[bold cyan]Proceed with video splitting?[/bold cyan]", default=True):
            console.print("[yellow]Operation cancelled by user[/yellow]")
            return

        console.print("\n[bold cyan]Starting video processing...[/bold cyan]")
        results = process_video_segments(input_video, output_path, segments, config)

        display_results(results)

        def is_failed(result):
            return (result.startswith("Failed") or result.startswith("Exception"))

        successful = all(not is_failed(result) for _, result in results)

        if successful:
            console.print("\n[bold green]All segments processed successfully![/bold green]")
        else:
            console.print("\n[bold yellow]Some segments failed to process. Check the results above.[/bold yellow]")

    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {e}")
        logger.exception(f"Main execution error: {e}")
    finally:
        console.print("\n[bold green]Video splitting completed.[/bold green]")
        wait_for_user_exit()


if __name__ == "__main__":
    main()
