# editing-optimized-example.yaml
# Professional video editing workflow configuration

input_video: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/2025.01.22-kl.10.49.34--Perspektiv_merged_compressed.mp4"
use_subdirectory: true
output_dir: "C:/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__VideoSplitter/video_splitter/timecodes"

ffmpeg_path: "ffmpeg"
ffprobe_path: "ffprobe"

# ENCODING PROFILES FOR VIDEO EDITING WORKFLOWS
# ==============================================

# RECOMMENDED: All-Intra H.264 for frame-accurate editing
# - Every frame is a keyframe (I-frame) for precise cuts
# - CRF 18 provides visually lossless quality
# - Optimized for Premiere Pro, DaVinci Resolve, Final Cut Pro
codec_profile: "h264_intra"
output_format: "mp4"

# QUALITY CONTROL FOR FILE SIZE
# =============================
# Quality setting (0-51): Controls file size vs quality trade-off
# - 0: Lossless (largest files, perfect quality)
# - 18: Visually lossless (default, excellent quality)
# - 23: Standard quality (good balance)
# - 28: Smaller files (good quality, noticeable compression)
# - 35: Much smaller files (acceptable quality, visible artifacts)
quality: 18

# ALTERNATIVE: ProRes 422 HQ for professional workflows
# Uncomment the lines below for ProRes output:
# codec_profile: "prores_hq"
# output_format: "mov"  # ProRes requires MOV container

# ALTERNATIVE: Fast stream copy for quick previews (smallest files)
# codec_profile: "fast_copy"
# Note: fast_copy ignores quality setting

# ALTERNATIVE: Legacy After Effects compatibility
# codec_profile: "legacy_ae"

threads: 4
append_timestamps: true
include_source_prefix: false

# TECHNICAL NOTES:
# ================
# h264_intra settings:
#   - All-Intra encoding (-g 1) ensures every frame is seekable
#   - CRF 18 provides visually lossless quality
#   - PCM audio avoids compression artifacts
#   - BT.709 color space prevents color shifts
#
# prores_hq settings:
#   - ProRes 422 HQ profile for high-quality intermediate files
#   - Uncompressed PCM audio
#   - Industry standard for professional editing
#
# fast_copy settings:
#   - Stream copy for maximum speed
#   - May have seeking issues on P-frames and B-frames
#   - Best for quick previews or when frame accuracy isn't critical

# TIMECODE FORMAT: HH:MM:SS or HH:MM:SS.ms for frame-accurate cutting
# Examples:
#   "00:00:17" = 17 seconds
#   "00:04:44" = 4 minutes 44 seconds
#   "00:00:17.500" = 17.5 seconds (500ms)
#   "00:01:30.250" = 1 minute 30.25 seconds (250ms)

segments:
  - start: "00:00:00.00"
    end: "00:00:17.00"
    title: "01-a-starte videoopptak"

  - start: "00:00:34.00"
    end: "00:04:44.00"
    title: "02-b-dele perspektiv"

  - start: "00:04:54.00"
    end: "00:06:43.20"
    title: "03-c-det uperfekte, hånd i hånd"

  - start: "00:07:01.16"
    end: "00:07:39.00"
    title: "04-d1-oppmerksomheten vår"

  - start: "00:07:42.12"
    end: "00:08:05.15"
    title: "05-d2-oppmerksomheten vår"

  - start: "00:08:10.11"
    end: "00:09:38.29"
    title: "06-d3-oppmerksomheten vår"

  - start: "00:09:42.27"
    end: "00:10:25.26"
    title: "07-d4-oppmerksomheten vår"

  - start: "00:10:24.29"
    end: "00:12:58.10"
    title: "08-d5-oppmerksomheten vår"

  - start: "00:13:01.26"
    end: "00:13:14.28"
    title: "09-d5-oppmerksomheten vår"

  - start: "00:13:26.09"
    end: "00:13:40.03"
    title: "10-politikere-mennesker med makt"

  - start: "00:13:51.09"
    end: "00:14:32.10"
    title: "11-politikere-blir farget av markedet"

  - start: "00:14:32.10"
    end: "00:15:14.20"
    title: "12-kaos, man blir aldri helt fri fra det"

  - start: "00:15:14.20"
    end: "00:16:27.17"
    title: "13-utsikten fra vinduet på røyse"

  - start: "00:19:45.12"
    end: "00:24:48.12"
    title: "14-la oss være mennesker sammen"

  - start: "00:24:50.27"
    end: "00:25:50.18"
    title: "15-trenger aldri å bli helt svart "

  - start: "00:25:50.18"
    end: "00:28:00.08"
    title: "16-meditasjon, lurer meg selv"

  #
  - start: "00:28:00.08"
    end: "00:29:49.21"
    title: "17-vi må tørre å være rare"

  - start: "00:30:02.26"
    end: "00:30:53.17"
    title: "18-jeg legger ut brødsmulene"
