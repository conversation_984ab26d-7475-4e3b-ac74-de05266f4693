<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Console Output Replica</title>
    <style>
    :root {
        /* Adjust these colors if needed */
        --color-bg: #000000;
        --color-fg: #e6e6e6;
        --color-green: #00ff00;
        --color-cyan: #00ffff;
        --color-highlight: #ffff00;
        --color-path: #7092f5;
        --color-dim: #bbbbbb;
    }


    body {
        background: var(--color-bg);
        color: var(--color-fg);
        font-family: Consolas, "Courier New", monospace;
        margin: 20px;
    }

    pre {
        white-space: pre;
        font-size: 14px;
        line-height: 1.4em;
        color: var(--color-fg);
    }

    /* Classes for applying colors */
    .green   { color: var(--color-green); }
    .cyan    { color: var(--color-cyan); }
    .dim     { color: var(--color-dim); }
    .path    { color: var(--color-path); }
    .highlight { color: var(--color-highlight); font-weight: bold; }
</style>
</head>
<body>
<pre>
<span class="green">[11:03:45]</span> Logging initialized. Log file: split_video.log
Interactive Mode:

Enter the path to the input video file (): <span class="path">C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\output_segments\fwfwe.mp4</span>
Enter the output directory for video segments (<span class="dim">output_segments</span>):
Do you want to provide a timecodes CSV or YAML file? [y/n] (n):

Video Duration: <span class="highlight">05:00</span> | Acceptable Formats: MM:SS, SS, MMSS, SS

Enter timecodes for splitting the video:

Segment 1 name (clip1):
Start time: 00:30
End time: 01:30
<span class="green">Added segment 'clip1': 30 to 01:30</span>

Do you want to add another segment? [y/n] (n):

Input Video: <span class="path">C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\output_segments\fwfwe.mp4</span> | Output Directory: <span class="dim">output_segments</span> | Number of Segments: <span class="dim">1</span>

<span class="green">Segment 'clip1' created successfully.</span>

Splitting Summary

┌───────────────┬─────────────────────────────────────┬──────────┐
│ Segment Name  │ Output Path                         │ Status   │
├───────────────┼─────────────────────────────────────┼──────────┤
│ clip1         │ output_segments\clip1.mp4           │ Success  │
└───────────────┴─────────────────────────────────────┴──────────┘
</pre>
</body>
</html>
