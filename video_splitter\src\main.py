import argparse
import os
import subprocess
import sys
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

import yaml
from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.progress import (Progress, SpinnerColumn, TextColumn,
                          BarColumn, TimeElapsedColumn)
from rich.prompt import Prompt, Confirm
from rich.table import Table

console = Console()


class TimecodeParser:
    """Handles normalization and parsing of timecodes."""

    @staticmethod
    def normalize_timecode(time_str: str, require_hours: bool = False) -> str:
        """Enhanced timecode parsing with explicit format detection.

        Formats supported:
        - HH:MM:SS.mmm - Hours:Minutes:Seconds.Milliseconds
        - MM:SS.mmm - Minutes:Seconds.Milliseconds
        - SS.mmm - Seconds.Milliseconds
        - Smart detection for ambiguous cases like 00:17:30
        """
        time_str = time_str.strip()

        # Handle decimal seconds (e.g., "17.30", "1:30.5")
        if '.' in time_str:
            return TimecodeParser._parse_decimal_timecode(time_str, require_hours)

        if ':' in time_str:
            parts = time_str.split(':')
            if require_hours and len(parts) != 3:
                raise ValueError("Invalid format: use HH:MM:SS.")

            if len(parts) == 2:
                # MM:SS format - but check for seconds.milliseconds interpretation
                minutes_part, seconds_part = parts
                minutes_val, seconds_val = int(minutes_part), int(seconds_part)

                # Smart detection: if "seconds" > 59, treat as MM:SS
                # If both parts are <= 59 and first part has leading zeros, treat as SS.mmm
                if seconds_val >= 60:
                    # Definitely MM:SS format (seconds > 59)
                    hours = '00'
                    minutes, seconds = minutes_part, seconds_part
                elif minutes_part == '00' and minutes_val <= 59 and seconds_val <= 99:
                    # Treat as SS.mmm format (e.g., "00:17:30" = 17.30 seconds)
                    hours = '00'
                    minutes = '00'
                    total_seconds = minutes_val + (seconds_val / 100.0)
                    return f"00:00:{total_seconds:06.3f}"
                else:
                    # Standard MM:SS format
                    hours = '00'
                    minutes, seconds = minutes_part, seconds_part

            elif len(parts) == 3:
                hours, minutes, seconds = parts
                hours_val, minutes_val, seconds_val = int(hours), int(minutes), int(seconds)

                # Standard HH:MM:SS format
                hours, minutes, seconds = hours, minutes, seconds

            elif len(parts) == 1:
                hours = '00'
                minutes = '00'
                seconds = parts[0]
            else:
                raise ValueError("Invalid format: HH:MM:SS, MM:SS, or SS expected.")
        else:
            # Handle numeric-only formats (HHMMSS, MMSS, SS)
            return TimecodeParser._parse_numeric_timecode(time_str, require_hours)

        # Validate components
        hours, minutes, seconds = int(hours), int(minutes), int(seconds)
        if not (0 <= minutes < 60) or not (0 <= seconds < 60) or hours < 0:
            raise ValueError("Invalid time components.")

        if require_hours or hours > 0:
            return f"{hours:02}:{minutes:02}:{seconds:02}"
        elif minutes > 0:
            return f"{minutes:02}:{seconds:02}"
        else:
            return f"{seconds:02}"

    @staticmethod
    def _parse_decimal_timecode(time_str: str, require_hours: bool = False) -> str:
        """Parse timecode with decimal seconds (e.g., '17.30', '1:30.5')."""
        if ':' in time_str:
            # Format like "1:30.5" or "0:17.30"
            parts = time_str.split(':')
            if len(parts) == 2:
                minutes_part, seconds_part = parts
                minutes = int(minutes_part)
                seconds = float(seconds_part)
                total_seconds = minutes * 60 + seconds
                return f"00:00:{total_seconds:06.3f}"
            else:
                raise ValueError("Decimal format supports MM:SS.mmm only.")
        else:
            # Format like "17.30" (seconds.milliseconds)
            seconds = float(time_str)
            return f"00:00:{seconds:06.3f}"

    @staticmethod
    def _parse_numeric_timecode(time_str: str, require_hours: bool = False) -> str:
        """Parse numeric-only timecode (HHMMSS, MMSS, SS)."""
        import re
        digits = ''.join(re.findall(r'\d+', time_str))
        length = len(digits)

        if require_hours:
            if length != 6:
                raise ValueError("Invalid length: use HHMMSS.")
            hours, minutes, seconds = digits[:2], digits[2:4], digits[4:6]
        else:
            if length == 6:  # HHMMSS
                hours, minutes, seconds = digits[:2], digits[2:4], digits[4:6]
            elif length == 4:  # MMSS
                hours = '00'
                minutes, seconds = digits[:2], digits[2:4]
            elif length == 2:  # SS
                hours = '00'
                minutes = '00'
                seconds = digits
            else:
                raise ValueError("Invalid length: use HHMMSS, MMSS, or SS.")

        return f"{hours}:{minutes}:{seconds}"

    @staticmethod
    def parse_timecode(time_str: str, require_hours: bool = False) -> float:
        """Parse timecode and return total seconds as float for precision."""
        normalized = TimecodeParser.normalize_timecode(time_str, require_hours)
        parts = normalized.split(':')

        if len(parts) == 3:
            hours, minutes = int(parts[0]), int(parts[1])
            # Handle decimal seconds
            seconds = float(parts[2]) if '.' in parts[2] else int(parts[2])
        elif len(parts) == 2:
            hours = 0
            minutes = int(parts[0])
            seconds = float(parts[1]) if '.' in parts[1] else int(parts[1])
        else:
            hours = 0
            minutes = 0
            seconds = float(parts[0]) if '.' in parts[0] else int(parts[0])

        return hours * 3600 + minutes * 60 + seconds

    @staticmethod
    def format_duration(seconds: float, require_hours: bool = False) -> str:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        if require_hours or hours > 0:
            return f"{hours:02}:{minutes:02}:{secs:02}"
        elif minutes > 0:
            return f"{minutes:02}:{secs:02}"
        else:
            return f"{secs:02}"

    @staticmethod
    def format_seconds(seconds: int, require_hours: bool = False) -> str:
        return TimecodeParser.format_duration(seconds, require_hours)


@dataclass
class VideoSegment:
    """Represents a single video segment."""
    name: str
    start: str
    end: str


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Split a video into segments using FFmpeg")
    parser.add_argument("-i", "--input_video", type=str, help="Path to input video file")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
    parser.add_argument("-tc", "--timecodes_file", type=str, help="Path to YAML/CSV timecodes file")
    parser.add_argument("--ffmpeg_path", default="ffmpeg", help="Path to ffmpeg executable")
    parser.add_argument("--ffprobe_path", default="ffprobe", help="Path to ffprobe executable")
    parser.add_argument("--output_format", default="mp4", help="Output video format")
    parser.add_argument("--codec", default="copy", help="Video codec (copy, libx264, etc.)")
    parser.add_argument("--threads", type=int, default=1, help="Number of concurrent threads")
    parser.add_argument("--after_effects_compatible", action="store_true", 
                        help="Re-encode for Adobe After Effects compatibility")
    parser.add_argument("--append_timestamps", action="store_true", default=True,
                        help="Append timestamps to output filenames")
    parser.add_argument("--include_source_prefix", action="store_true", default=False,
                        help="Include source filename as prefix")
    parser.add_argument("--use_subdirectory", action="store_true", default=False,
                        help="Create subdirectory for output files")
    parser.add_argument("--prompt", action="store_true", help="Interactive mode")
    return parser.parse_args()

def setup_logger():
    """Initialize logging with loguru."""
    logger.remove()
    logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
    logger.add("video_splitter.log", level="DEBUG", format="{time} - {level} - {message}")

def cleanup_logs():
    """Clean up log files after successful execution."""
    import time
    log_file = "video_splitter.log"

    # Remove all logger handlers to release file handles
    logger.remove()

    # Give the system a moment to release the file handle (Windows issue)
    time.sleep(0.1)

    if os.path.exists(log_file):
        # Try multiple times with increasing delays
        for attempt in range(3):
            try:
                os.remove(log_file)
                console.print("[bold green]Successfully cleaned up log file[/bold green]")
                return
            except PermissionError:
                if attempt < 2:  # Not the last attempt
                    time.sleep(0.2 * (attempt + 1))  # 0.2s, 0.4s delays
                    continue
                else:
                    # On final attempt, just silently skip cleanup
                    console.print("[dim]Log file cleanup skipped (file in use)[/dim]")
                    return
            except Exception as e:
                console.print(f"[bold red]Error cleaning up log file: {str(e)}[/bold red]")
                return

def wait_for_user_exit():
    """Wait for user input before exiting."""
    console.print("\n[dim]Press Enter to exit...[/dim]")
    input()

def get_video_duration(video_path: str, ffprobe_path: str = "ffprobe") -> float:
    """Get video duration using ffprobe."""
    cmd = [
        ffprobe_path, "-v", "error", "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1", video_path
    ]
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return float(result.stdout.decode().strip())
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Failed to get video duration: {e.stderr.decode().strip()}")

def load_timecodes_from_yaml(file_path: str) -> Dict[str, Any]:
    """Load configuration and segments from YAML file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f) or {}
    
    segments = []
    for seg in data.get('segments', []):
        if seg.get('start') and seg.get('end') and (seg.get('title') or seg.get('name')):
            segments.append((
                seg['start'].strip(),
                seg['end'].strip(), 
                (seg.get('title') or seg.get('name')).strip()
            ))
    
    return {
        'input_video': data.get('input_video'),
        'output_dir': data.get('output_dir'),
        'segments': segments,
        'ffmpeg_path': data.get('ffmpeg_path', 'ffmpeg'),
        'ffprobe_path': data.get('ffprobe_path', 'ffprobe'),
        'output_format': data.get('output_format', 'mp4'),
        'codec': data.get('codec', 'copy'),
        'threads': data.get('threads', 1),
        'after_effects_compatible': data.get('after_effects_compatible', False),
        'append_timestamps': data.get('append_timestamps', True),
        'include_source_prefix': data.get('include_source_prefix', False),
        'use_subdirectory': data.get('use_subdirectory', False)
    }

def prompt_for_segments(video_duration: float) -> List[Tuple[str, str, str]]:
    """Interactively prompt user for video segments with validation."""
    formatted_dur = TimecodeParser.format_duration(video_duration)
    require_hours = video_duration >= 3600
    fmts = ("HH:MM:SS, MM:SS, SS, HHMMSS, MMSS" if require_hours
            else "MM:SS, SS, MMSS")

    console.print(f"\n[bold cyan]Video Duration:[/bold cyan] [dim]{formatted_dur}[/dim] | "
                  f"[bold cyan]Formats:[/bold cyan] [dim]{fmts}[/dim]\n")
    console.print("[bold cyan]Enter segments:[/bold cyan]\n")

    result = []
    idx = 1

    while True:
        name = Prompt.ask(f"[bold cyan]Segment {idx} name[/bold cyan]",
                         default=f"clip{idx}")

        # Get start time with validation
        start_sec = None
        while start_sec is None:
            st = Prompt.ask("[bold cyan]Start time[/bold cyan]")
            try:
                start_sec = TimecodeParser.parse_timecode(st, require_hours)
            except ValueError as v:
                console.print(f"[bold red]{v}[/bold red]")

        # Get end time with validation
        end_sec = None
        while end_sec is None:
            en = Prompt.ask("[bold cyan]End time[/bold cyan]")
            try:
                en_sec = TimecodeParser.parse_timecode(en, require_hours)
                if en_sec <= start_sec:
                    raise ValueError("End must be greater than start.")
                if en_sec > video_duration:
                    raise ValueError("End exceeds video duration.")
                end_sec = en_sec
            except ValueError as v:
                console.print(f"[bold red]{v}[/bold red]")

        # Format and add segment
        st_form = TimecodeParser.format_seconds(start_sec, require_hours)
        en_form = TimecodeParser.format_seconds(end_sec, require_hours)
        result.append((st_form, en_form, name))
        console.print(f"[bold green]Added '{name}': {st_form} to {en_form}[/bold green]\n")
        idx += 1

        if not Confirm.ask("[bold cyan]Add another segment?[/bold cyan]", default=False):
            break

    return result


def discover_and_select_timecode_file() -> Optional[str]:
    """Discover available timecode files and let user select interactively."""
    timecodes_dir = Path("timecodes")

    if not timecodes_dir.exists():
        console.print(f"[bold yellow]Timecodes directory '{timecodes_dir}' not found[/bold yellow]")
        return None

    # Find YAML files in timecodes directory
    yaml_files = list(timecodes_dir.glob("*.yaml")) + list(timecodes_dir.glob("*.yml"))

    if not yaml_files:
        console.print(f"[bold yellow]No YAML timecode files found in '{timecodes_dir}'[/bold yellow]")
        return None

    # Display available files in a table
    console.print(f"\n[bold cyan]Available timecode files in '{timecodes_dir}':[/bold cyan]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("No.", style="cyan", width=4)
    table.add_column("Filename", style="green")
    table.add_column("Size", style="dim", width=8)

    for idx, file_path in enumerate(yaml_files, 1):
        file_size = f"{file_path.stat().st_size}B" if file_path.exists() else "N/A"
        table.add_row(str(idx), file_path.name, file_size)

    console.print(table)

    # Prompt for selection
    while True:
        try:
            choice = Prompt.ask(
                f"[bold cyan]Select timecode file (1-{len(yaml_files)}) or 'q' to skip[/bold cyan]",
                default="q"
            )

            if choice.lower() == 'q':
                return None

            file_idx = int(choice) - 1
            if 0 <= file_idx < len(yaml_files):
                selected_file = str(yaml_files[file_idx])
                console.print(f"[bold green]Selected: {selected_file}[/bold green]")
                return selected_file
            else:
                console.print(f"[bold red]Invalid selection. Choose 1-{len(yaml_files)} or 'q'[/bold red]")
        except ValueError:
            console.print(f"[bold red]Invalid input. Enter a number (1-{len(yaml_files)}) or 'q'[/bold red]")


def check_output_directory(output_dir: str):
    """Check if output directory exists and create if needed."""
    if not Path(output_dir).exists():
        if Confirm.ask(f"[bold cyan]Create output directory '{output_dir}'?[/bold cyan]",
                      default=True):
            try:
                os.makedirs(output_dir, exist_ok=True)
                console.print(f"[bold green]Created directory: {output_dir}[/bold green]\n")
            except Exception as e:
                console.print(f"[bold red]Error creating directory: {e}[/bold red]")
                sys.exit(1)
        else:
            console.print("[yellow]Operation cancelled by user[/yellow]")
            sys.exit(1)


def get_user_inputs(args):
    """Get and validate user inputs, with interactive prompts if needed."""
    yaml_config = {}

    # Load YAML configuration if provided or prompt for it
    if args.prompt and not args.timecodes_file:
        if Confirm.ask("[bold cyan]Load a YAML/CSV timecodes file?[/bold cyan]",
                      default=True):
            while True:
                timecodes_path = Prompt.ask("[bold cyan]Path to timecodes file (or press Enter to browse)[/bold cyan]",
                                          default="")

                # If user pressed Enter (empty input), show file browser
                if not timecodes_path.strip():
                    selected_file = discover_and_select_timecode_file()
                    if selected_file:
                        args.timecodes_file = selected_file
                    break  # Exit loop whether file was selected or user chose to skip

                # User entered a specific path
                if Path(timecodes_path).is_file():
                    args.timecodes_file = timecodes_path
                    break
                else:
                    console.print(f"[bold red]File not found: {timecodes_path}[/bold red]")
                    # Continue the loop to retry - no discovery here

    if args.timecodes_file and Path(args.timecodes_file).is_file():
        try:
            yaml_config = load_timecodes_from_yaml(args.timecodes_file)
            console.print(f"[bold green]Loaded configuration from {args.timecodes_file}[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error loading YAML file: {e}[/bold red]")

    # Prompt for input video
    if args.prompt or not args.input_video:
        default_video = yaml_config.get('input_video') or args.input_video or ""
        args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]",
                                    default=default_video)
        while not Path(args.input_video).is_file():
            console.print("[bold red]File not found. Try again.[/bold red]")
            args.input_video = Prompt.ask("[bold cyan]Input video file[/bold cyan]")

    if not args.input_video or not Path(args.input_video).is_file():
        console.print("[bold red]Error: Input video file not found[/bold red]")
        sys.exit(1)

    # Interactive prompts for additional options if in prompt mode
    if args.prompt:
        use_subdir = Confirm.ask(
            "[bold cyan]Place output in a separate subdirectory?[/bold cyan]",
            default=yaml_config.get("use_subdirectory", False)
        )
        if use_subdir:
            args.output_path = Prompt.ask(
                "[bold cyan]Output directory[/bold cyan]",
                default=yaml_config.get("output_dir", "output_segments")
            )
        else:
            args.output_path = str(Path(args.input_video).parent)

        args.append_timestamps = Confirm.ask(
            "[bold cyan]Append timestamps to filenames?[/bold cyan]",
            default=yaml_config.get("append_timestamps", True)
        )
        args.include_source_prefix = Confirm.ask(
            "[bold cyan]Include source file's name as prefix?[/bold cyan]",
            default=yaml_config.get("include_source_prefix", False)
        )
        args.after_effects_compatible = Confirm.ask(
            "[bold cyan]Re-encode segments for Adobe After Effects compatibility?[/bold cyan]",
            default=yaml_config.get("after_effects_compatible", False)
        )
    elif not args.output_path:
        default_output = yaml_config.get('output_dir') or args.output_path
        if not default_output:
            if yaml_config.get('use_subdirectory', False):
                default_output = "output_segments"
            else:
                default_output = str(Path(args.input_video).parent)
        args.output_path = default_output

    # Handle segments
    segments = yaml_config.get('segments', [])
    if args.prompt or not segments:
        if segments:
            console.print(f"[bold green]Found {len(segments)} segments from YAML/CSV.[/bold green]")
            if args.prompt:
                use_existing = Confirm.ask("[bold cyan]Use these segments as-is?[/bold cyan]",
                                         default=True)
                if not use_existing:
                    discard = Confirm.ask(
                        "[bold cyan]Discard loaded segments and define new ones?[/bold cyan]",
                        default=True
                    )
                    if discard:
                        segments = []

        if not segments:
            try:
                duration = get_video_duration(args.input_video,
                                            yaml_config.get('ffprobe_path', args.ffprobe_path))
                segments = prompt_for_segments(duration)
            except Exception as e:
                console.print(f"[bold red]Error getting video duration: {e}[/bold red]")
                sys.exit(1)

    # Merge YAML config with CLI args (CLI takes precedence)
    for key, value in yaml_config.items():
        if not hasattr(args, key) or getattr(args, key) is None:
            setattr(args, key, value)

    return args.input_video, args.output_path, segments, args

def split_video_segment(input_video: str, segment: VideoSegment, output_dir: str,
                       ffmpeg_path: str, output_format: str, codec: str,
                       append_timestamps: bool, include_source_prefix: bool,
                       after_effects_compatible: bool) -> Tuple[str, Optional[str], Optional[str]]:
    """Split a single video segment using FFmpeg."""

    source_name = Path(input_video).stem if include_source_prefix else ""
    prefix = f"{source_name}_" if include_source_prefix else ""
    
    if append_timestamps:
        start_clean = segment.start.replace(':', '-')
        end_clean = segment.end.replace(':', '-')
        filename = f"{prefix}{segment.name}_{start_clean}_to_{end_clean}.{output_format}"
    else:
        filename = f"{prefix}{segment.name}.{output_format}"
    
    output_path = Path(output_dir) / filename
    
    if after_effects_compatible:
        cmd = [
            ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
            "-c:v", "libx264", "-preset", "faster", "-pix_fmt", "yuv420p",
            "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart", "-y", str(output_path)
        ]
    else:
        # Optimized settings for video editing compatibility and frame-accurate playback
        if codec == "copy":
            # Stream copy - fast but may have seeking issues
            cmd = [
                ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
                "-c", "copy", "-avoid_negative_ts", "make_zero", "-y", str(output_path)
            ]
        else:
            # Corrected optimal settings for video editing workflows
            cmd = [
                ffmpeg_path, "-i", input_video, "-ss", segment.start, "-to", segment.end,
                "-c:v", codec, "-preset", "medium", "-crf", "18", "-g", "6", "-bf", "0",
                "-pix_fmt", "yuv420p", "-colorspace", "bt709", "-color_primaries", "bt709",
                "-color_trc", "bt709", "-color_range", "mpeg",
                "-c:a", "aac", "-b:a", "192k", "-ar", "48000",
                "-avoid_negative_ts", "make_zero", "-movflags", "+faststart+write_colr",
                "-y", str(output_path)
            ]
    


    try:
        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return segment.name, str(output_path), None
    except subprocess.CalledProcessError as e:
        return segment.name, None, e.stderr.decode().strip()


def process_video_segments(input_video: str, output_dir: str,
                           segments: List[Tuple[str, str, str]],
                           config: argparse.Namespace) -> List[Tuple[str, str]]:
    """Process all video segments with progress tracking."""
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    segment_objects = [VideoSegment(name, start, end)
                       for start, end, name in segments]
    results = []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console,
        transient=True
    ) as progress:
        task = progress.add_task(
            "[bold cyan]Processing video segments...[/bold cyan]",
            total=len(segment_objects)
        )

        with ThreadPoolExecutor(max_workers=config.threads) as executor:
            future_to_segment = {
                executor.submit(
                    split_video_segment,
                    input_video,
                    segment,
                    output_dir,
                    config.ffmpeg_path,
                    config.output_format,
                    config.codec,
                    config.append_timestamps,
                    config.include_source_prefix,
                    config.after_effects_compatible
                ): segment.name for segment in segment_objects
            }

            for future in as_completed(future_to_segment):
                segment_name = future_to_segment[future]
                try:
                    name, output_path, error = future.result()
                    if output_path:
                        results.append((name, output_path))
                        console.print(f"[bold green]✓ Completed: {name}[/bold green]")
                    else:
                        results.append((name, f"Failed: {error}"))
                        console.print(f"[bold red]✗ Failed: {name}[/bold red]")
                except Exception as e:
                    error_msg = f"Exception: {str(e)}"
                    results.append((segment_name, error_msg))
                    console.print(f"[bold red]✗ Exception: {segment_name}[/bold red]")
                finally:
                    progress.advance(task)

    return results

def display_summary(input_video: str, output_dir: str,
                    segments: List[Tuple[str, str, str]],
                    config: argparse.Namespace):
    """Display operation summary before processing."""
    table = Table(title="Video Splitting Configuration",
                  show_header=False, box=None)
    table.add_column("Parameter", style="bold cyan")
    table.add_column("Value", style="dim")

    table.add_row("Input Video", input_video)
    table.add_row("Output Directory", output_dir)
    table.add_row("Output Format", config.output_format)
    table.add_row("Codec", config.codec)
    table.add_row("Threads", str(config.threads))
    ae_compat = "Yes" if config.after_effects_compatible else "No"
    table.add_row("AE Compatible", ae_compat)
    append_ts = "Yes" if config.append_timestamps else "No"
    table.add_row("Append Timestamps", append_ts)
    include_prefix = "Yes" if config.include_source_prefix else "No"
    table.add_row("Include Source Prefix", include_prefix)
    table.add_row("Segments", str(len(segments)))

    console.print("\n[bold blue]--- Operation Summary ---[/bold blue]")
    console.print(Panel(table, border_style="blue"))

    if segments:
        console.print("\n[bold cyan]Segments to process:[/bold cyan]")
        seg_table = Table(show_header=True, header_style="bold magenta")
        seg_table.add_column("Name", style="cyan")
        seg_table.add_column("Start", style="green")
        seg_table.add_column("End", style="green")

        for start, end, name in segments:
            seg_table.add_row(name, start, end)
        console.print(seg_table)


def display_results(results: List[Tuple[str, str]]):
    """Display processing results."""
    table = Table(title="Processing Results", show_header=True,
                  header_style="bold cyan")
    table.add_column("Segment", style="cyan")
    table.add_column("Status", style="dim")
    table.add_column("Output", style="dim")

    for name, result in results:
        if result.startswith("Failed") or result.startswith("Exception"):
            table.add_row(name, "[bold red]Failed[/bold red]", result)
        else:
            table.add_row(name, "[bold green]Success[/bold green]", result)

    console.print("\n")
    console.print(table)


def main():
    """Main entry point."""
    args = parse_arguments()
    input_video, output_path, segments, config = get_user_inputs(args)

    setup_logger()

    try:
        # Check and create output directory
        check_output_directory(output_path)

        # Display summary and get confirmation
        display_summary(input_video, output_path, segments, config)

        proceed_msg = "\n[bold cyan]Proceed with video splitting?[/bold cyan]"
        if not Confirm.ask(proceed_msg, default=True):
            console.print("[yellow]Operation cancelled by user[/yellow]")
            return

        # Process video segments
        console.print("\n[bold cyan]Starting video processing...[/bold cyan]")
        results = process_video_segments(input_video, output_path,
                                         segments, config)

        # Display results
        display_results(results)

        # Check if all segments were successful
        def is_failed(result):
            return (result.startswith("Failed") or
                    result.startswith("Exception"))

        successful = all(not is_failed(result) for _, result in results)

        if successful:
            success_msg = ("\n[bold green]All segments processed "
                           "successfully![/bold green]")
            console.print(success_msg)
            cleanup_logs()
        else:
            fail_msg = ("\n[bold yellow]Some segments failed to process. "
                        "Check the results above.[/bold yellow]")
            console.print(fail_msg)

    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {e}")
        logger.exception(f"Main execution error: {e}")
    finally:
        console.print("\n[bold green]Video splitting completed.[/bold green]")
        wait_for_user_exit()




if __name__ == "__main__":
    main()
